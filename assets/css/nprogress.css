#nprogress {
    pointer-events: none;
}

@keyframes rainbow-progress {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

#nprogress .bar {
    position: fixed;
    z-index: 1031;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(
        124deg,
        #ff0000 0%,
        #ff9900 15%,
        #ffff00 30%,
        #00ff00 45%,
        #00ffff 60%,
        #0000ff 75%,
        #9900ff 90%,
        #ff0000 100%
    );
    background-size: 200% 200%;
    animation: rainbow-progress 2s ease infinite;
}

/* 去掉右上角的旋转动画 */
#nprogress .spinner {
    display: none;
}

/* 进度条上的光晕效果 */
#nprogress .peg {
    display: block;
    position: absolute;
    right: 0;
    width: 100px;
    height: 100%;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 0 0 5px rgba(255, 255, 255, 0.5);
    opacity: 1.0;
    transform: rotate(3deg) translate(0px, -4px);
} 