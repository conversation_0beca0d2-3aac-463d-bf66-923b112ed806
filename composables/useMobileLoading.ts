export const useMobileLoading = () => {
  const visible = ref(false)
  const text = ref('加载中...')

  const show = (loadingText?: string) => {
    if (loadingText) {
      text.value = loadingText
    }
    visible.value = true
  }

  const hide = () => {
    visible.value = false
  }

  const setText = (newText: string) => {
    text.value = newText
  }

  return {
    visible,
    text,
    show,
    hide,
    setText
  }
} 