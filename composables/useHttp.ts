import { FetchError } from 'ofetch';
import { MessagePlugin } from 'tdesign-vue-next';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';

// 配置 NProgress
NProgress.configure({ 
    easing: 'ease',
    speed: 500,
    showSpinner: false,
    trickleSpeed: 200,
    minimum: 0.1
});

interface ApiResponse<T = any> {
  code: number;
  data: T;
  message: string;
}

interface HttpOptions {
  /**
   * 是否显示错误提示
   * @default true
   */
  showError?: boolean;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  params?: Record<string, any>;
  body?: any;
  headers?: Record<string, string>;
  baseURL?: string;
  onRequest?: (ctx: any) => void;
  onResponse?: (ctx: any) => void;
  onRequestError?: (ctx: any) => void;
  onResponseError?: (ctx: any) => void;
}

/**
 * 创建通用HTTP请求composable
 */
export const useHttp = () => {
  /**
   * 发起GET请求
   * @param url 请求地址
   * @param params 请求参数
   * @param options 请求选项
   */
  const get = async <T = any>(url: string, params: Record<string, any> = {}, options: HttpOptions = {}): Promise<T> => {
    return request<T>(url, { method: 'GET', params, ...options });
  };

  /**
   * 发起POST请求
   * @param url 请求地址
   * @param data 请求体
   * @param options 请求选项
   */
  const post = async <T = any>(url: string, data: Record<string, any> = {}, options: HttpOptions = {}): Promise<T> => {
    return request<T>(url, { method: 'POST', body: data, ...options });
  };

  /**
   * 发起PUT请求
   * @param url 请求地址
   * @param data 请求体
   * @param options 请求选项
   */
  const put = async <T = any>(url: string, data: Record<string, any> = {}, options: HttpOptions = {}): Promise<T> => {
    return request<T>(url, { method: 'PUT', body: data, ...options });
  };

  /**
   * 发起DELETE请求
   * @param url 请求地址
   * @param params 请求参数
   * @param options 请求选项
   */
  const del = async <T = any>(url: string, params: Record<string, any> = {}, options: HttpOptions = {}): Promise<T> => {
    return request<T>(url, { method: 'DELETE', params, ...options });
  };

  /**
   * 通用请求方法
   * @param url 请求地址
   * @param options 请求选项
   */
  const request = async <T = any>(url: string, options: HttpOptions = {}): Promise<T> => {
    const { showError = true, ...fetchOptions } = options;

    // 开始加载进度条
    NProgress.start();

    try {
      // 获取认证token
      const { authToken } = useAuth();
      //console.log('useHttp: authToken', authToken.value);
      
      // 准备headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };
      
      // 添加认证token
      if (authToken.value) {
        headers['Authorization'] = `Bearer ${authToken.value}`;
        //console.log('Setting Authorization header:', `Bearer ${authToken.value}`);
      }
      
      // 使用Nuxt提供的$fetch函数发送请求
      const response = await $fetch<ApiResponse<T>>(url, {
        ...fetchOptions,
        headers,
        // 响应拦截器
        onResponse({ response }) {
          // 请求成功，结束进度条
          NProgress.done();
        },
        // 响应错误拦截器
        onResponseError({ response }) {
          // 请求失败，结束进度条
          NProgress.done();
        }
      });

      // 假设后端返回的数据格式为 { code, data, message }
      if (response.code !== 0) {
        // 业务逻辑错误处理
        const error = new Error(response.message || '请求失败');
        if (showError) {
          // 可以在此集成消息提示组件
          console.error(response.message || '请求失败');
          MessagePlugin.error(response.message || '请求失败');
        }
        throw error;
      }

      return response.data;
    } catch (error) {
      // 确保在错误发生时也结束进度条
      NProgress.done();

      if (error instanceof FetchError) {
        // 网络请求错误处理
        const status = error.response?.status;
        let message = '请求失败';

        if (status === 401) {
          message = '未授权，请重新登录';
          // 可以在此处理登录失效逻辑
          const { logout } = useAuth();
          logout();
          navigateTo('/');
        } else if (status === 403) {
          message = '禁止访问';
        } else if (status === 404) {
          message = '请求资源不存在';
        } else if (status === 500) {
          message = '服务器错误';
        } else if (status === 400) {
          message = '请求参数错误';
        }

        if (showError) {
          // 可以在此集成消息提示组件
          console.error(message);
          MessagePlugin.error(message);
        }
      }
      
      throw error;
    }
  };

  /**
   * 上传文件
   * @param url 上传地址
   * @param file 文件对象
   * @param options 请求选项
   */
  const uploadFile = async <T = any>(url: string, file: File, options: HttpOptions = {}): Promise<T> => {
    const formData = new FormData();
    formData.append('file', file);
    
    return request<T>(url, { 
      method: 'POST', 
      body: formData,
      ...options 
    });
  };

  return {
    get,
    post,
    put,
    del,  // 改名为del避免与JavaScript保留字delete冲突
    request,
    uploadFile
  };
};
