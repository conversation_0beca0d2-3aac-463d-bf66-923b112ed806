import { parseJwtToken, isTokenExpired } from '~/utils/jwt'

export interface UserInfo {
  userId: number
  name: string
  phone: string
  roleId: number
  roleName: string
  permissions: string[]
}

export const useUserInfo = () => {
  const userInfo = ref<UserInfo | null>(null)
  const isLoggedIn = ref(false)

  // 从token中加载用户信息
  const loadUserInfo = () => {
    const token = useCookie('auth-token', {
      default: () => ''
    })

    if (!token.value) {
      userInfo.value = null
      isLoggedIn.value = false
      return
    }

    // 检查token是否过期
    if (isTokenExpired(token.value)) {
      // token过期，清除
      token.value = ''
      userInfo.value = null
      isLoggedIn.value = false
      return
    }

    // 解析token获取用户信息
    const tokenData = parseJwtToken(token.value)
    if (tokenData) {
      userInfo.value = {
        userId: tokenData.userId,
        name: tokenData.name || '用户',
        phone: tokenData.phone || '',
        roleId: tokenData.roleId,
        roleName: tokenData.roleName || '',
        permissions: tokenData.permissions || []
      }
      isLoggedIn.value = true
    } else {
      userInfo.value = null
      isLoggedIn.value = false
    }
  }

  // 清除用户信息
  const clearUserInfo = () => {
    const token = useCookie('auth-token')
    token.value = ''
    userInfo.value = null
    isLoggedIn.value = false
  }

  // 检查用户是否有特定权限
  const hasPermission = (permission: string): boolean => {
    return userInfo.value?.permissions.includes(permission) || false
  }

  // 检查用户是否有任一权限
  const hasAnyPermission = (permissions: string[]): boolean => {
    if (!userInfo.value?.permissions) return false
    return permissions.some(permission => userInfo.value!.permissions.includes(permission))
  }

  return {
    userInfo: readonly(userInfo),
    isLoggedIn: readonly(isLoggedIn),
    loadUserInfo,
    clearUserInfo,
    hasPermission,
    hasAnyPermission
  }
} 