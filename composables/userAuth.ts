// composables/useAuth.ts
export type User = {
  id: number;
  name: string;
  phone?: string;
  role?: {
    id: number;
    name: string;
  };
  permissions?: string[];
} | null;

// SSR-friendly ref for the token
const useAuthToken = () => useState<string | null>("auth_token", () => null);

export const useAuth = () => {
  const user = useState<User>("user", () => null);
  const authToken = useAuthToken(); // 使用我们定义的 token 状态

  // 在客户端加载 token (例如从 localStorage)
  // 仅在客户端执行，以避免 SSR 问题
  if (import.meta.client && !authToken.value) {
    const storedToken = localStorage.getItem("auth_token");
    if (storedToken) {
      authToken.value = storedToken;
    }
  }

  const setToken = (newToken: string | null) => {
    authToken.value = newToken;
    if (import.meta.client) {
      if (newToken) {
        localStorage.setItem("auth_token", newToken);
      } else {
        localStorage.removeItem("auth_token");
      }
    }
  };

  const fetchUser = async () => {
    if (!authToken.value) {
      user.value = null;
      return;
    }
    const { get } = useHttp();
    try {
      const data = await get<User>("/api/auth/me");
      user.value = data;
    } catch (error: any) {
      // console.error('Failed to fetch user:', error.data?.message || error.message);
      // 如果 token 无效 (例如 401)，清除它
      if (error.response?.status === 401) {
        setToken(null); // 清除无效的 token
      }
      user.value = null;
    }
  };

  const login = async (credentials: { phone: string; password: string }) => {
    const { post } = useHttp();
    try {
      const response = await post("/api/auth/login", credentials);
      if (response.token && response.user) {
        setToken(response.token); // <--- 存储 token
        user.value = response.user;
        navigateTo("/dashboard");
      } else {
        throw new Error("Login response did not include token or user.");
      }
    } catch (error: any) {
      user.value = null;
      setToken(null); // 登录失败也清除 token
      console.error("Login failed:", error.data?.message || error.message);
      throw error;
    }
  };

  const logout = async () => {
    try {
      // 可选：通知后端，如果后端有黑名单机制
      // await $fetch('/api/auth/logout', {
      //   method: 'POST',
      //   headers: { 'Authorization': `Bearer ${authToken.value}` } // 如果后端需要知道是哪个token登出
      // });
    } catch (error) {
      // 即便后端登出失败，客户端也应清除状态
      console.error(
        "Server logout failed (continuing client-side logout):",
        error
      );
    } finally {
      user.value = null;
      setToken(null); // <--- 清除 token
      navigateTo("/");
    }
  };

  // 计算属性，判断是否登录
  const isLoggedIn = computed(() => !!user.value && !!authToken.value);

  return {
    user,
    authToken, // 可以暴露 token 供其他地方使用（例如，自定义的 $fetch 拦截器）
    fetchUser,
    login,
    logout,
    isLoggedIn,
  };
};
