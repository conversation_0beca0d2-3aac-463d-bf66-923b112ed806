import { useAuth } from "~/composables/userAuth";

export const usePermission = () => {
  const { user } = useAuth();

  // 检查当前用户是否有指定权限
  const hasPermission = (permissionCode: string): boolean => {
    if (!user.value || !user.value.permissions) {
      return false;
    }
    return user.value.permissions.includes(permissionCode);
  };

  // 检查当前用户是否有指定权限中的任意一个
  const hasAnyPermission = (permissionCodes: string[]): boolean => {
    if (!user.value || !user.value.permissions) {
      return false;
    }
    return permissionCodes.some(code => user.value?.permissions?.includes(code) || false);
  };

  // 检查当前用户是否有指定权限中的所有权限
  const hasAllPermissions = (permissionCodes: string[]): boolean => {
    if (!user.value || !user.value.permissions) {
      return false;
    }
    return permissionCodes.every(code => user.value?.permissions?.includes(code) || false);
  };

  // 检查当前用户是否是某个角色
  const hasRole = (roleName: string): boolean => {
    if (!user.value || !user.value.role) {
      return false;
    }
    return user.value.role.name === roleName;
  };

  // 检查当前用户是否是某些角色中的一个
  const hasAnyRole = (roleNames: string[]): boolean => {
    if (!user.value || !user.value.role) {
      return false;
    }
    return roleNames.includes(user.value.role.name);
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
  };
}; 