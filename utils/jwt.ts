/**
 * 解析JWT token获取用户信息
 * @param token JWT token
 * @returns 解析后的用户信息
 */
export function parseJwtToken(token: string) {
  try {
    // JWT token由三部分组成，用.分隔：header.payload.signature
    const parts = token.split('.')
    if (parts.length !== 3) {
      throw new Error('Invalid JWT token format')
    }

    // 解析payload部分（第二部分）
    const payload = parts[1]
    
    // Base64解码
    const decodedPayload = atob(payload)
    
    // 解析JSON
    const userInfo = JSON.parse(decodedPayload)
    
    return userInfo
  } catch (error) {
    console.error('Failed to parse JWT token:', error)
    return null
  }
}

/**
 * 检查token是否过期
 * @param token JWT token
 * @returns 是否过期
 */
export function isTokenExpired(token: string): boolean {
  try {
    const userInfo = parseJwtToken(token)
    if (!userInfo || !userInfo.exp) {
      return true
    }
    
    // exp是秒级时间戳，需要转换为毫秒
    const expirationTime = userInfo.exp * 1000
    const currentTime = Date.now()
    
    return currentTime >= expirationTime
  } catch (error) {
    return true
  }
} 