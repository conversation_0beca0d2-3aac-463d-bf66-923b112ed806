import dayjs from "dayjs";

export const formatDate = (date: string) => {
  /*  if (!date) return '-';
    //如果时分秒为0，则不显示
    if (dayjs(date).hour() === 0 && dayjs(date).minute() === 0 && dayjs(date).second() === 0) {
        return dayjs(date).format('YYYY-MM-DD');
    } else {
        return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
    } */
 /*  if (!date) return "-";
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  const hours = String(d.getHours()).padStart(2, "0");
  const minutes = String(d.getMinutes()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}`; */

  // 2025-06-09T21:50:51.000Z -> 2025-06-09 21:50:51
  if (!date) return "-";
  const dateStr = date.split("T")[0];
  const timeStr = date.split("T")[1].split(".")[0];
  const timeStrWithoutSeconds = timeStr.split(":")[0] + ":" + timeStr.split(":")[1];
  return `${dateStr} ${timeStrWithoutSeconds}`;
};

export const formatDateToYMD = (date: string) => {
  if (!date) return "-";
  /* return dayjs(date).format("YYYY-MM-DD"); */
 /*  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`; */

  // 2025-06-09T21:50:51.000Z -> 2025-06-09
  const dateStr = date.split("T")[0];
  return dateStr;
};
