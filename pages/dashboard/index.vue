<template>
 <!--  <div>
    <div class="dashboard-stats">
      <div class="stat-card">
        <div class="stat-icon">👥</div>
        <div class="stat-info">
          <div class="stat-value">2,345</div>
          <div class="stat-label">用户总数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">📦</div>
        <div class="stat-info">
          <div class="stat-value">856</div>
          <div class="stat-label">产品数量</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">📝</div>
        <div class="stat-info">
          <div class="stat-value">328</div>
          <div class="stat-label">待处理订单</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">💰</div>
        <div class="stat-info">
          <div class="stat-value">¥128,590</div>
          <div class="stat-label">本月收入</div>
        </div>
      </div>
    </div>
    
    <div class="recent-activity">
      <h3 class="section-title">最近活动</h3>
      <div class="activity-list">
        <div class="activity-item">
          <div class="activity-time">10:25</div>
          <div class="activity-content">新用户注册: 张三</div>
        </div>
        <div class="activity-item">
          <div class="activity-time">09:40</div>
          <div class="activity-content">新订单 #38291 已创建</div>
        </div>
        <div class="activity-item">
          <div class="activity-time">09:15</div>
          <div class="activity-content">产品库存更新: 手机壳 +50</div>
        </div>
        <div class="activity-item">
          <div class="activity-time">昨天</div>
          <div class="activity-content">系统维护完成</div>
        </div>
      </div>
    </div>
  </div> -->
</template>

<script setup>
definePageMeta({
  layout: 'dashboard'
});
</script>

<style scoped>
/* 统计卡片样式 */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.stat-icon {
  font-size: 24px;
  margin-right: 15px;
  width: 50px;
  height: 50px;
  background-color: #f1f1f9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-value {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  color: #6c757d;
  font-size: 14px;
}

/* 最近活动样式 */
.recent-activity {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.section-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
}

.activity-item {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  width: 60px;
  color: #6c757d;
}
</style>

