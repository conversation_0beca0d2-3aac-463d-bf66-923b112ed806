<template>
    <div class="contracts-container">
        <t-card title="合同管理">
            <template #actions>
                <t-button theme="primary" @click="handleAddContract">
                    <template #icon>
                        <t-icon name="add" />
                    </template>
                    新增合同
                </t-button>
            </template>

            <t-table :data="contracts" :columns="columns" :loading="loading" :pagination="pagination" row-key="id"
                @page-change="onPageChange">
                <template #contractStatus="{ row }">
                    <t-tag :theme="row.contractStatus === 1 ? 'success' : 'danger'" variant="light">
                        {{ row.contractStatus === 1 ? '正常' : '已过期' }}
                    </t-tag>
                </template>

                <template #contractAmount="{ row }">
                    ¥{{ Number(row.contractAmount).toLocaleString() }}
                </template>

                <template #contractDates="{ row }">
                    <div>
                        <div>{{ formatDate(row.contractStartDate) }}</div>
                        <div>至</div>
                        <div>{{ formatDate(row.contractEndDate) }}</div>
                    </div>
                </template>

                <template #lands="{ row }">
                    <div v-if="row.lands && row.lands.length > 0">
                        <t-tag v-for="land in row.lands" :key="land.landId" size="small" style="margin: 2px;">
                            {{ land.landName }}({{ land.landArea }}m²)
                        </t-tag>
                    </div>
                    <span v-else class="text-gray-400">-</span>
                </template>

                <template #op="slotProps">
                    <t-space>
                        <t-button theme="primary" variant="text" @click="handleEdit(slotProps.row)">
                            编辑
                        </t-button>
                        <t-button theme="danger" variant="text" @click="handleDelete(slotProps.row)">
                            删除
                        </t-button>
                    </t-space>
                </template>
            </t-table>
        </t-card>

        <!-- 新增/编辑合同抽屉 -->
        <ClientOnly>
            <t-drawer v-model:visible="drawerVisible" :size="drawerSize" :header="drawerTitle" :footer="true"
                @close="onDrawerClose">
                <template #body>
                    <t-form ref="formRef" :data="formData" :rules="rules" @submit="onSubmit" label-align="top">
                        <t-form-item label="合同编号" name="contractNumber">
                            <t-input v-model="formData.contractNumber" placeholder="请输入合同编号" />
                        </t-form-item>

                        <t-form-item label="租户" name="tenantId">
                            <t-select v-model="formData.tenantId" placeholder="请选择租户" :options="tenantOptions" />
                        </t-form-item>

                        <t-form-item label="合同金额(元)" name="contractAmount">
                            <t-input v-model="formData.contractAmount" placeholder="请输入合同金额" />
                        </t-form-item>

                        <t-form-item label="合同开始日期" name="contractStartDate">
                            <t-date-picker v-model="formData.contractStartDate" placeholder="请选择开始日期" />
                        </t-form-item>

                        <t-form-item label="合同结束日期" name="contractEndDate">
                            <t-date-picker v-model="formData.contractEndDate" placeholder="请选择结束日期" />
                        </t-form-item>

                        <t-form-item label="关联地块" name="landIds">
                            <t-checkbox-group v-model="formData.landIds">
                                <div class="land-checkbox-container">
                                    <t-checkbox v-for="land in availableLands" :key="land.id" :value="land.id">
                                        {{ land.name }}({{ land.area }}m²)
                                    </t-checkbox>
                                </div>
                            </t-checkbox-group>
                        </t-form-item>

                        <t-form-item label="合同状态" name="contractStatus">
                            <t-radio-group v-model="formData.contractStatus" :options="statusOptions" />
                        </t-form-item>
                    </t-form>
                </template>

                <template #footer>
                    <t-space>
                        <t-button theme="default" variant="base" @click="onDrawerClose">取消</t-button>
                        <t-button theme="primary" @click="handleSubmit" :loading="submitting">确定</t-button>
                    </t-space>
                </template>
            </t-drawer>
        </ClientOnly>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import type { FormRules } from 'tdesign-vue-next';

definePageMeta({
    layout: 'dashboard'
});

const contracts = ref([]);
const loading = ref(false);
const pagination = ref({
    total: 0,
    pageSize: 10,
    current: 1,
});

// 抽屉相关
const drawerVisible = ref(false);
const drawerSize = '600px';
const drawerTitle = ref('新增合同');
const submitting = ref(false);
const isEdit = ref(false);
const formRef = ref();

// 选项数据
const tenantOptions = ref([]);
const availableLands = ref([]);

interface ContractFormData {
    id: string | null;
    contractNumber: string;
    tenantId: string;
    contractAmount: string;
    contractStartDate: string;
    contractEndDate: string;
    landIds: string[];
    contractStatus: number;
}

// 表单数据
const formData = ref<ContractFormData>({
    id: null,
    contractNumber: '',
    tenantId: '',
    contractAmount: '',
    contractStartDate: '',
    contractEndDate: '',
    landIds: [],
    contractStatus: 1
});

// 状态选项
const statusOptions = [
    { label: '正常', value: 1 },
    { label: '已过期', value: 2 }
];

// 表单规则
const rules: FormRules<ContractFormData> = {
    contractNumber: [
        { required: true, message: '请输入合同编号', type: 'error' },
        { min: 1, max: 50, message: '合同编号长度在1-50个字符', type: 'error' }
    ],
    tenantId: [
        { required: true, message: '请选择租户', type: 'error' }
    ],
    contractAmount: [
        { required: true, message: '请输入合同金额', type: 'error' },
        { 
            pattern: /^\d+(\.\d{1,2})?$/, 
            message: '请输入正确的金额格式', 
            type: 'error' 
        }
    ],
    contractStartDate: [
        { required: true, message: '请选择合同开始日期', type: 'error' }
    ],
    contractEndDate: [
        { required: true, message: '请选择合同结束日期', type: 'error' }
    ]
};

// 定义表格列
const columns = [
    {
        colKey: 'contractNumber',
        title: '合同编号',
        width: 150,
    },
    {
        colKey: 'tenantName',
        title: '租户',
        width: 120,
    },
    {
        colKey: 'contractAmount',
        title: '合同金额',
        width: 120,
        cell: 'contractAmount',
    },
    {
        colKey: 'contractDates',
        title: '合同期限',
        width: 200,
        cell: 'contractDates',
    },
    {
        colKey: 'lands',
        title: '关联地块',
        width: 200,
        cell: 'lands',
    },
    {
        colKey: 'contractStatus',
        title: '状态',
        width: 100,
        cell: 'contractStatus',
    },
    {
        colKey: 'op',
        title: '操作',
        width: 160,
        cell: 'op',
    },
];

interface Contract {
    id: string;
    contractNumber: string;
    tenantId: string;
    tenantName: string;
    contractAmount: string;
    contractStartDate: string;
    contractEndDate: string;
    contractStatus: number;
    lands: Array<{
        landId: string;
        landName: string;
        landArea: string;
    }>;
}

// 获取合同列表
const fetchContracts = async () => {
    loading.value = true;
    try {
        const { post } = useHttp();
        const response = await post('/api/contracts/list', {
            pageSize: pagination.value.pageSize,
            current: pagination.value.current
        });
        
        contracts.value = response.list;
        pagination.value.total = response.total;
    } catch (error) {
        MessagePlugin.error('获取合同列表失败');
    } finally {
        loading.value = false;
    }
};

// 获取租户选项
const fetchTenants = async () => {
    try {
        const { get } = useHttp();
        const response = await get('/api/contracts/tenants');
        tenantOptions.value = response.map((tenant: any) => ({
            label: `${tenant.name}(${tenant.phone})`,
            value: tenant.id
        }));
    } catch (error) {
        MessagePlugin.error('获取租户列表失败');
    }
};

// 获取可用地块
const fetchAvailableLands = async () => {
    try {
        const { get } = useHttp();
        const response = await get('/api/contracts/available-lands');
        availableLands.value = response;
    } catch (error) {
        MessagePlugin.error('获取可用地块失败');
    }
};

// 格式化日期
const formatDate = (date: string | Date) => {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleDateString('zh-CN');
};

// 页码变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
    pagination.value.current = pageInfo.current;
    pagination.value.pageSize = pageInfo.pageSize;
    fetchContracts();
};

// 添加合同
const handleAddContract = async () => {
    isEdit.value = false;
    drawerTitle.value = '新增合同';
    formData.value = {
        id: null,
        contractNumber: '',
        tenantId: '',
        contractAmount: '',
        contractStartDate: '',
        contractEndDate: '',
        landIds: [],
        contractStatus: 1
    };
    await fetchTenants();
    await fetchAvailableLands();
    drawerVisible.value = true;
};

// 编辑合同
const handleEdit = async (contract: Contract) => {
    isEdit.value = true;
    drawerTitle.value = '编辑合同';

    // 获取当前合同的关联地块
    const { post } = useHttp();
    const contractLands = await post('/api/contracts/contract-lands', { contractId: contract.id });
    const currentLandIds = contractLands.map((land: any) => land.landId);

    // 获取可用地块（包含当前合同已关联的地块）
    await fetchAvailableLands();
    
    // 添加当前合同关联的地块到可选地块中
    for (const land of contractLands) {
        if (!availableLands.value.find((l: any) => l.id === land.landId)) {
            availableLands.value.push({
                id: land.landId,
                name: land.landName,
                area: land.landArea
            });
        }
    }

    formData.value = {
        id: contract.id,
        contractNumber: contract.contractNumber,
        tenantId: contract.tenantId,
        contractAmount: contract.contractAmount,
        contractStartDate: contract.contractStartDate,
        contractEndDate: contract.contractEndDate,
        landIds: currentLandIds,
        contractStatus: contract.contractStatus
    };

    await fetchTenants();
    drawerVisible.value = true;
};

// 删除合同
const handleDelete = (contract: Contract) => {
    const confirm = DialogPlugin.confirm({
        header: '确认删除',
        body: `确定要删除合同"${contract.contractNumber}"吗？删除后关联的地块将变为未租状态！`,
        theme: 'danger',
        confirmBtn: {
            content: '确认删除',
            theme: 'danger',
        },
        cancelBtn: {
            content: '取消',
            theme: 'default',
        },
        onConfirm: async () => {
            try {
                const { post } = useHttp();
                await post('/api/contracts/delete', { id: contract.id });

                MessagePlugin.success('删除成功');
                fetchContracts();
            } catch (error) {
                MessagePlugin.error('删除失败');
            } finally {
                confirm.hide();
            }
        }
    });
};

// 关闭抽屉
const onDrawerClose = () => {
    drawerVisible.value = false;
    formData.value = {
        id: null,
        contractNumber: '',
        tenantId: '',
        contractAmount: '',
        contractStartDate: '',
        contractEndDate: '',
        landIds: [],
        contractStatus: 1
    };
    isEdit.value = false;
    // 重置表单验证状态
    formRef.value?.reset();
};

// 手动触发表单验证
const handleSubmit = () => {
    formRef.value?.submit();
};

// 提交表单
const onSubmit = async ({ validateResult, firstError }: { validateResult: boolean, firstError: string }) => {
    if (validateResult !== true) {
        MessagePlugin.error(firstError);
        return;
    }

    // 验证日期
    if (new Date(formData.value.contractEndDate) <= new Date(formData.value.contractStartDate)) {
        MessagePlugin.error('合同结束日期必须晚于开始日期');
        return;
    }

    try {
        submitting.value = true;
        const { post } = useHttp();
        const url = isEdit.value ? '/api/contracts/update' : '/api/contracts/save';
        await post(url, formData.value);
        
        MessagePlugin.success(isEdit.value ? '更新成功' : '创建成功');
        drawerVisible.value = false;
        fetchContracts();
    } catch (error) {
        MessagePlugin.error(isEdit.value ? '更新失败' : '创建失败');
    } finally {
        submitting.value = false;
    }
};

// 页面加载时获取合同列表
onMounted(() => {
    fetchContracts();
});
</script>

<style scoped>
.contracts-container {
    padding: 20px;
}

.land-checkbox-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.text-gray-400 {
    color: #9ca3af;
}
</style>