<template>
  <div class="photo-container">
    <ClientOnly>
      <div v-if="!mounted" class="loading">
        <t-loading text="加载中..." />
      </div>
      <div v-else>
    <!-- 页面标题和操作按钮 -->
    <div class="header">
      <h1>照片管理</h1>
      <t-button theme="primary" @click="handleUploadPhoto">
        <template #icon>
          <t-icon name="upload" />
        </template>
        上传照片
      </t-button>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-form">
      <t-form layout="inline" :data="searchForm" @submit="handleSearch" @reset="handleReset">
        <t-form-item label="地块筛选">
          <t-select 
            v-model="searchForm.landId" 
            placeholder="选择地块" 
            clearable
            style="width: 200px;"
          >
            <t-option
              v-for="land in availableLands"
              :key="land.landId"
              :value="land.landId"
              :label="`${land.landName} (${land.tenantName})`"
            />
          </t-select>
        </t-form-item>
        <t-form-item>
          <t-button theme="primary" type="submit">搜索</t-button>
          <t-button theme="default" type="reset" style="margin-left: 8px;">重置</t-button>
        </t-form-item>
      </t-form>
    </div>

    <!-- 照片相册 - 按地块分组显示 -->
    <div v-if="loading" class="loading">
      <t-loading text="加载中..." />
    </div>

    <div v-else-if="!photoGroups || photoGroups.length === 0" class="empty">
      <t-empty description="暂无照片数据" />
    </div>

    <div v-else class="photo-groups">
      <div v-for="group in photoGroups" :key="group.landId || 'unknown'" class="photo-group">
        <div class="group-header">
          <h3>{{ group.landName || '未知地块' }}</h3>
          <span class="photo-count">{{ (group.photos || []).length }} 张照片</span>
        </div>
        
        <div v-if="group.photos && group.photos.length > 0" class="photo-grid">
          <div 
            v-for="photo in group.photos" 
            :key="photo.id || Math.random()" 
            class="photo-item"
            @click="handlePreviewPhoto(photo)"
          >
            <div class="photo-wrapper">
              <img :src="photo.url" :alt="photo.name || '照片'" />
              <div class="photo-overlay">
                <div class="photo-info">
                  <p class="photo-name">{{ photo.name || '未知照片' }}</p>
                  <p class="photo-meta">
                    <span v-if="photo.cropName">{{ photo.cropEmoji || '' }} {{ photo.cropName }}</span>
                    <span>{{ formatDate(photo.createdAt) }}</span>
                  </p>
                </div>
                <div class="photo-actions">
                  <t-button 
                    theme="danger" 
                    size="small" 
                    variant="text"
                    @click.stop="handleDeletePhoto(photo)"
                  >
                    <template #icon>
                      <t-icon name="delete" />
                    </template>
                  </t-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="photoGroups && photoGroups.length > 0" class="pagination">
      <t-pagination
        v-model="pagination.current"
        :total="pagination.total"
        :page-size="pagination.pageSize"
        :show-sizer="true"
        :page-size-options="[10, 20, 50]"
        @change="onPageChange"
        @page-size-change="onPageSizeChange"
      />
    </div>

    <!-- 上传照片对话框 -->
    <t-dialog
      v-model:visible="uploadDialogVisible"
      title="上传照片"
      width="600px"
      @confirm="handleUploadSubmit"
      @cancel="handleUploadCancel"
      :confirm-btn="{ content: '确认上传', loading: uploading }"
    >
      <t-form ref="uploadFormRef" :data="uploadForm" :rules="uploadRules" label-width="100px">
        <t-form-item label="选择地块" name="landId">
          <t-select 
            v-model="uploadForm.landId" 
            placeholder="请选择地块"
            @change="handleLandChange"
          >
            <t-option
              v-for="land in availableLands"
              :key="land.landId"
              :value="land.landId"
              :label="`${land.landName} (${land.tenantName})`"
            />
          </t-select>
        </t-form-item>

        <t-form-item label="关联作物" name="cropId">
          <t-select 
            v-model="uploadForm.cropId" 
            placeholder="选择作物（可选）"
            clearable
            :disabled="!uploadForm.landId || !selectedLandCrops.length"
          >
            <t-option
              v-for="crop in selectedLandCrops"
              :key="crop.id"
              :value="crop.id"
              :label="`${crop.emoji} ${crop.name}`"
            />
          </t-select>
        </t-form-item>

        <t-form-item label="上传照片" name="files">
          <div class="upload-container">
            <input
              ref="fileInputRef"
              type="file"
              multiple
              accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
              style="display: none;"
              @change="handleFileSelect"
            />
            <t-button theme="primary" @click="selectFiles">
              <template #icon>
                <t-icon name="upload" />
              </template>
              选择图片
            </t-button>
            <div v-if="selectedFiles.length > 0" class="selected-files">
              <div v-for="(file, index) in selectedFiles" :key="index" class="file-item">
                <img :src="file.preview" :alt="file.name" class="file-preview" />
                <div class="file-info">
                  <p class="file-name">{{ file.name }}</p>
                  <p class="file-size">{{ formatFileSize(file.size) }}</p>
                </div>
                <t-button
                  theme="danger"
                  size="small"
                  variant="text"
                  @click="removeFile(index)"
                >
                  <template #icon>
                    <t-icon name="close" />
                  </template>
                </t-button>
              </div>
            </div>
            <p class="upload-tips">最多上传5张图片，每张不超过1MB，支持jpg、png、gif、webp格式</p>
          </div>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 图片预览对话框 -->
    <t-dialog
      v-model:visible="previewDialogVisible"
      title="照片预览"
      width="800px"
      :footer="false"
    >
      <div v-if="previewPhoto" class="photo-preview">
        <img :src="previewPhoto.url" :alt="previewPhoto.name" style="width: 100%; height: auto;" />
        <div class="preview-info">
          <h4>{{ previewPhoto.name }}</h4>
          <p><strong>地块：</strong>{{ previewPhoto.landName }}</p>
          <p v-if="previewPhoto.cropName">
            <strong>作物：</strong>{{ previewPhoto.cropEmoji }} {{ previewPhoto.cropName }}
          </p>
          <p><strong>租户：</strong>{{ previewPhoto.tenantName }}</p>
          <p><strong>上传时间：</strong>{{ formatDate(previewPhoto.createdAt) }}</p>
        </div>
      </div>
    </t-dialog>
      </div>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'

definePageMeta({
  layout: "dashboard"
})

// 响应式数据
const mounted = ref(false)
const loading = ref(false)
const uploading = ref(false)
const uploadDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const uploadFormRef = ref()
const fileInputRef = ref()
const selectedFiles = ref([])

// 搜索表单
const searchForm = reactive({
  landId: '',
})

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
})

// 照片数据
const photoGroups = ref([])
const availableLands = ref([])
const previewPhoto = ref(null)

// 上传表单
const uploadForm = reactive({
  landId: '',
  cropId: '',
})

// 表单验证规则
const uploadRules = {
  landId: [
    { required: true, message: '请选择地块', type: 'error' }
  ],
}

// 计算属性
const selectedLandCrops = computed(() => {
  if (!uploadForm.landId || !availableLands.value) return []
  const selectedLand = availableLands.value.find(land => land && land.landId === uploadForm.landId)
  return (selectedLand && selectedLand.crops) ? selectedLand.crops : []
})

// 获取照片列表
const fetchPhotos = async () => {
  loading.value = true
  try {
    const { post } = useHttp()
    const response = await post('/api/photos/list', {
      page: pagination.current,
      pageSize: pagination.pageSize,
      landId: searchForm.landId || undefined,
    })
    
    if (response && response.list) {
      photoGroups.value = response.list
      pagination.total = response.total || 0
    } else {
      photoGroups.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取照片列表错误:', error)
    photoGroups.value = []
    pagination.total = 0
    MessagePlugin.error('获取照片列表失败')
  } finally {
    loading.value = false
  }
}

// 获取可用地块列表
const fetchAvailableLands = async () => {
  try {
    const { get } = useHttp()
    const response = await get('/api/lands/available')
    if (response && response.lands) {
      availableLands.value = response.lands
    } else {
      availableLands.value = []
    }
  } catch (error) {
    console.error('获取可用地块错误:', error)
    availableLands.value = []
    MessagePlugin.error('获取可用地块失败')
  }
}

// 页码变化
const onPageChange = (pageInfo) => {
  if (pageInfo && typeof pageInfo === 'object') {
    pagination.current = pageInfo.current || pageInfo
    pagination.pageSize = pageInfo.pageSize || pagination.pageSize
    fetchPhotos()
  }
}

// 页面大小变化
const onPageSizeChange = (size) => {
  if (size && typeof size === 'number') {
    pagination.pageSize = size
    pagination.current = 1
    fetchPhotos()
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchPhotos()
}

// 重置搜索
const handleReset = () => {
  searchForm.landId = ''
  pagination.current = 1
  fetchPhotos()
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) return '-'
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return '-'
  }
}

// 上传照片
const handleUploadPhoto = () => {
  // 重置表单数据
  uploadForm.landId = ''
  uploadForm.cropId = ''
  selectedFiles.value = []
  
  // 显示对话框
  uploadDialogVisible.value = true
}

// 地块变化
const handleLandChange = () => {
  uploadForm.cropId = '' // 清空作物选择
}

// 选择文件
const selectFiles = () => {
  fileInputRef.value?.click()
}

// 处理文件选择
const handleFileSelect = (event) => {
  const files = Array.from(event.target.files || [])
  
  if (files.length === 0) return
  
  // 检查文件数量
  if (selectedFiles.value.length + files.length > 5) {
    MessagePlugin.error('最多只能选择5张图片')
    return
  }
  
  // 处理每个文件
  files.forEach(file => {
    // 检查文件大小
    if (file.size > 1024 * 1024) {
      MessagePlugin.error(`文件 ${file.name} 超过1MB限制`)
      return
    }
    
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      MessagePlugin.error(`文件 ${file.name} 不是支持的图片格式`)
      return
    }
    
    // 创建预览
    const reader = new FileReader()
    reader.onload = (e) => {
      selectedFiles.value.push({
        file,
        name: file.name,
        size: file.size,
        preview: e.target?.result
      })
    }
    reader.readAsDataURL(file)
  })
  
  // 清空input
  event.target.value = ''
}

// 移除文件
const removeFile = (index) => {
  selectedFiles.value.splice(index, 1)
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 手动上传单个文件
const uploadSingleFile = async (fileData) => {
  try {
    const formData = new FormData()
    formData.append('files', fileData.file)
    
    const response = await fetch('/api/photos/upload', {
      method: 'POST',
      body: formData
    })
    
    const result = await response.json()
    
    if (result.code === 0 && result.data.files && result.data.files.length > 0) {
      const uploadedFile = result.data.files[0]
      return {
        success: true,
        url: uploadedFile.url,
        name: uploadedFile.originalName
      }
    } else {
      throw new Error(result.message || '上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 确认上传
const handleUploadSubmit = async () => {
  try {
    // 表单验证
    const validateResult = await uploadFormRef.value?.validate()
    if (validateResult !== true) {
      return
    }

    if (!selectedFiles.value.length) {
      MessagePlugin.error('请选择要上传的照片')
      return
    }

    uploading.value = true

    // 逐个上传文件并保存到数据库
    const { post } = useHttp()
    let successCount = 0
    
    for (const fileData of selectedFiles.value) {
      try {
        // 上传文件到S3
        const uploadResult = await uploadSingleFile(fileData)
        
        if (uploadResult.success) {
          // 保存照片记录到数据库
          await post('/api/photos/save', {
            name: uploadResult.name || fileData.name || '未知照片',
            url: uploadResult.url,
            landId: uploadForm.landId,
            cropId: uploadForm.cropId || undefined,
          })
          successCount++
        } else {
          MessagePlugin.error(`文件 ${fileData.name} 上传失败: ${uploadResult.error}`)
        }
      } catch (error) {
        console.error(`文件 ${fileData.name} 处理失败:`, error)
        MessagePlugin.error(`文件 ${fileData.name} 处理失败`)
      }
    }

    if (successCount > 0) {
      MessagePlugin.success(`成功上传 ${successCount} 张照片`)
      uploadDialogVisible.value = false
      // 重置表单
      uploadForm.landId = ''
      uploadForm.cropId = ''
      selectedFiles.value = []
      fetchPhotos() // 刷新列表
    } else {
      MessagePlugin.error('没有照片上传成功')
    }
  } catch (error) {
    console.error('上传照片失败:', error)
    MessagePlugin.error('上传照片失败')
  } finally {
    uploading.value = false
  }
}

// 取消上传
const handleUploadCancel = () => {
  uploadDialogVisible.value = false
}

// 预览照片
const handlePreviewPhoto = (photo) => {
  if (photo && photo.url) {
    previewPhoto.value = photo
    previewDialogVisible.value = true
  }
}

// 删除照片
const handleDeletePhoto = (photo) => {
  const confirm = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除照片"${photo.name}"吗？删除后数据不可恢复！`,
    theme: 'danger',
    confirmBtn: {
      content: '确认删除',
      theme: 'danger',
    },
    cancelBtn: {
      content: '取消',
      theme: 'default',
    },
    onConfirm: async () => {
      try {
        const { post } = useHttp()
        await post('/api/photos/delete', { id: photo.id })

        MessagePlugin.success('删除成功')
        fetchPhotos()
      } catch (error) {
        MessagePlugin.error('删除失败')
      } finally {
        confirm.hide()
      }
    }
  })
}

// 页面加载时获取数据
onMounted(async () => {
  try {
    await fetchAvailableLands()
    await fetchPhotos()
    mounted.value = true
  } catch (error) {
    console.error('页面初始化错误:', error)
    mounted.value = true
  }
})
</script>

<style scoped>
.photo-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.photo-groups {
  margin-bottom: 20px;
}

.photo-group {
  margin-bottom: 30px;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  overflow: hidden;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e5e6eb;
}

.group-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.photo-count {
  color: #666;
  font-size: 14px;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  padding: 20px;
}

.photo-item {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s;
}

.photo-item:hover {
  transform: scale(1.02);
}

.photo-wrapper {
  position: relative;
  width: 100%;
  height: 200px;
  background-color: #f5f5f5;
}

.photo-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 15px;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.photo-item:hover .photo-overlay {
  transform: translateY(0);
}

.photo-info {
  margin-bottom: 10px;
}

.photo-name {
  font-weight: 500;
  margin: 0 0 5px 0;
  font-size: 14px;
}

.photo-meta {
  margin: 0;
  font-size: 12px;
  opacity: 0.9;
}

.photo-meta span {
  display: block;
}

.photo-actions {
  display: flex;
  justify-content: flex-end;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.photo-preview {
  text-align: center;
}

.preview-info {
  margin-top: 20px;
  text-align: left;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.preview-info h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.preview-info p {
  margin: 8px 0;
  color: #666;
}

/* 上传组件样式 */
.upload-container {
  padding: 20px;
  border: 2px dashed #e5e6eb;
  border-radius: 6px;
  text-align: center;
}

.selected-files {
  margin-top: 20px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.file-preview {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 15px;
}

.file-info {
  flex: 1;
  text-align: left;
}

.file-name {
  margin: 0 0 5px 0;
  font-weight: 500;
  color: #333;
}

.file-size {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.upload-tips {
  margin-top: 10px;
  font-size: 12px;
  color: #999;
}
</style> 