<template>
  <div class="photo-container">
    <ClientOnly>
      <div class="header">
        <h1>照片管理</h1>
        <t-button theme="primary" @click="handleUpload">上传照片</t-button>
      </div>

      <div v-if="loading" class="loading">
        <t-loading text="加载中..." />
      </div>

      <div v-else-if="!photos.length" class="empty">
        <t-empty description="暂无照片" />
      </div>

      <div v-else class="photo-list">
        <div v-for="photo in photos" :key="photo.id" class="photo-item">
          <img :src="photo.url" :alt="photo.name" />
          <div class="photo-info">
            <p>{{ photo.name }}</p>
            <p>{{ photo.landName }}</p>
          </div>
        </div>
      </div>
    </ClientOnly>
  </div>
</template>

<script setup>
definePageMeta({
  layout: "dashboard"
})

const loading = ref(false)
const photos = ref([])

const fetchPhotos = async () => {
  loading.value = true
  try {
    const response = await $fetch('/api/photos/list', {
      method: 'POST',
      body: { page: 1, pageSize: 20 }
    })
    
    if (response?.code === 0 && response?.data?.list) {
      // 展平分组数据
      const allPhotos = []
      response.data.list.forEach(group => {
        if (group.photos) {
          group.photos.forEach(photo => {
            allPhotos.push({
              ...photo,
              landName: group.landName
            })
          })
        }
      })
      photos.value = allPhotos
    }
  } catch (error) {
    console.error('获取照片失败:', error)
  } finally {
    loading.value = false
  }
}

const handleUpload = () => {
  console.log('上传照片')
}

onMounted(() => {
  fetchPhotos()
})
</script>

<style scoped>
.photo-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.loading, .empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.photo-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.photo-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.photo-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.photo-info {
  padding: 10px;
}

.photo-info p {
  margin: 5px 0;
  font-size: 14px;
}
</style> 