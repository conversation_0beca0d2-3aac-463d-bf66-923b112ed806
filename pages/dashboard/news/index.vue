<template>
  <div class="news-container">
    <t-card title="新闻管理">
      <template #actions>
        <t-button theme="primary" @click="handleAddNews">
          <template #icon>
            <t-icon name="add" />
          </template>
          新增新闻
        </t-button>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <t-form layout="inline" @submit="handleSearch" @reset="handleReset">
          <t-form-item label="新闻标题">
            <t-input v-model="searchForm.title" placeholder="请输入新闻标题" clearable style="width: 300px;" />
          </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" type="submit">
                <template #icon><t-icon name="search" /></template>
                查询
              </t-button>
              <t-button theme="default" type="reset">
                <template #icon><t-icon name="refresh" /></template>
                重置
              </t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </div>

      <t-table 
        :data="newsList" 
        :columns="columns" 
        :loading="loading" 
        :pagination="pagination" 
        row-key="id"
        @page-change="onPageChange">
        
        <template #createdAt="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>

        <template #content="{ row }">
          <div class="content-preview">
            {{ getContentPreview(row.content) }}
          </div>
        </template>

        <template #op="slotProps">
          <t-space>
            <t-button theme="primary" variant="text" @click="handleEdit(slotProps.row)">
              编辑
            </t-button>
            <t-button theme="danger" variant="text" @click="handleDelete(slotProps.row)">
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 新增/编辑新闻抽屉 -->
    <ClientOnly>
      <t-drawer 
        v-model:visible="drawerVisible" 
        :size="drawerSize" 
        :header="drawerTitle" 
        :footer="true"
        @close="onDrawerClose">
        
        <template #body>
          <t-form ref="formRef" :data="formData" :rules="rules" @submit="onSubmit" label-align="top">
            <t-form-item label="新闻标题" name="title">
              <t-input v-model="formData.title" placeholder="请输入新闻标题" />
            </t-form-item>

            <t-form-item label="新闻内容" name="content">
              <div class="editor-container">
                <QuillEditor
                  ref="quillEditor"
                  v-model:content="formData.content"
                  contentType="html"
                  placeholder="请输入新闻内容..."
                  theme="snow"
                  :toolbar="editorToolbar"
                  style="height: 300px;"
                />
              </div>
            </t-form-item>
          </t-form>
        </template>

        <template #footer>
          <t-space>
            <t-button theme="default" variant="base" @click="onDrawerClose">取消</t-button>
            <t-button theme="primary" @click="handleSubmit" :loading="submitting">确定</t-button>
          </t-space>
        </template>
      </t-drawer>
    </ClientOnly>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'

definePageMeta({
  layout: 'dashboard'
})

const newsList = ref([])
const loading = ref(false)
const pagination = ref({
  total: 0,
  pageSize: 10,
  current: 1,
})

// 搜索表单
const searchForm = ref({
  title: '',
})

// 抽屉相关
const drawerVisible = ref(false)
const drawerSize = '800px'
const drawerTitle = ref('新增新闻')
const submitting = ref(false)
const isEdit = ref(false)
const formRef = ref()
const quillEditor = ref()

// 富文本编辑器工具栏配置
const editorToolbar = [
  ['bold', 'italic', 'underline', 'strike'],
  ['blockquote', 'code-block'],
  [{ 'header': 1 }, { 'header': 2 }],
  [{ 'list': 'ordered'}, { 'list': 'bullet' }],
  [{ 'script': 'sub'}, { 'script': 'super' }],
  [{ 'indent': '-1'}, { 'indent': '+1' }],
  [{ 'size': ['small', false, 'large', 'huge'] }],
  [{ 'color': [] }, { 'background': [] }],
  [{ 'align': [] }],
  ['clean'],
  ['link', 'image']
]

// 表单数据
const formData = ref({
  id: null,
  title: '',
  content: '',
})

// 表单规则
const rules = {
  title: [
    { required: true, message: '请输入新闻标题', type: 'error' },
    { min: 2, max: 100, message: '新闻标题长度在2-100个字符', type: 'error' }
  ],
  content: [
    { required: true, message: '请输入新闻内容', type: 'error' },
    { min: 10, message: '新闻内容至少10个字符', type: 'error' }
  ]
}

// 定义表格列
const columns = [
  {
    colKey: 'title',
    title: '新闻标题',
    width: 200,
    ellipsis: true,
  },
  {
    colKey: 'content',
    title: '内容预览',
    width: 300,
    cell: 'content',
    ellipsis: true,
  },
  {
    colKey: 'createdAt',
    title: '创建时间',
    width: 150,
    cell: 'createdAt',
  },
  {
    colKey: 'op',
    title: '操作',
    width: 160,
    cell: 'op',
  },
]

// 获取新闻列表
const fetchNews = async () => {
  loading.value = true
  try {
    const { post } = useHttp()
    const response = await post('/api/news/list', {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      title: searchForm.value.title,
    })
    
    // useHttp会自动解包data字段，所以这里response就是data的内容
    newsList.value = response.list || []
    pagination.value.total = response.total || 0
  } catch (error) {
    console.error('获取新闻列表错误:', error)
    newsList.value = []
    pagination.value.total = 0
    MessagePlugin.error('获取新闻列表失败')
  } finally {
    loading.value = false
  }
}

// 页码变化
const onPageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current
  pagination.value.pageSize = pageInfo.pageSize
  fetchNews()
}

// 搜索
const handleSearch = () => {
  pagination.value.current = 1
  fetchNews()
}

// 重置搜索
const handleReset = () => {
  searchForm.value.title = ''
  pagination.value.current = 1
  fetchNews()
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 获取内容预览
const getContentPreview = (content) => {
  if (!content) return ''
  // 移除HTML标签，只保留纯文本用于预览
  const textContent = content.replace(/<[^>]*>/g, '')
  return textContent.length > 50 ? textContent.slice(0, 50) + '...' : textContent
}

// 添加新闻
const handleAddNews = () => {
  isEdit.value = false
  drawerTitle.value = '新增新闻'
  formData.value = {
    id: null,
    title: '',
    content: '',
  }
  drawerVisible.value = true
  
  // 延迟清空编辑器内容，确保编辑器已渲染
  nextTick(() => {
    if (quillEditor.value && quillEditor.value.setHTML) {
      quillEditor.value.setHTML('')
    }
  })
}

// 编辑新闻
const handleEdit = (news) => {
  isEdit.value = true
  drawerTitle.value = '编辑新闻'
  formData.value = {
    id: news.id,
    title: news.title,
    content: news.content,
  }
  drawerVisible.value = true
  
  // 延迟设置编辑器内容，确保编辑器已渲染
  nextTick(() => {
    if (quillEditor.value && quillEditor.value.setHTML) {
      quillEditor.value.setHTML(news.content || '')
    }
  })
}

// 删除新闻
const handleDelete = (news) => {
  const confirm = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除新闻"${news.title}"吗？删除后数据不可恢复！`,
    theme: 'danger',
    confirmBtn: {
      content: '确认删除',
      theme: 'danger',
    },
    cancelBtn: {
      content: '取消',
      theme: 'default',
    },
    onConfirm: async () => {
      try {
        const { post } = useHttp()
        await post('/api/news/delete', { id: news.id })

        MessagePlugin.success('删除成功')
        fetchNews()
      } catch (error) {
        MessagePlugin.error('删除失败')
      } finally {
        confirm.hide()
      }
    }
  })
}

// 关闭抽屉
const onDrawerClose = () => {
  drawerVisible.value = false
  formData.value = {
    id: null,
    title: '',
    content: '',
  }
  isEdit.value = false
  // 重置表单验证状态
  formRef.value?.reset()
  
  // 清空编辑器内容
  if (quillEditor.value && quillEditor.value.setHTML) {
    quillEditor.value.setHTML('')
  }
}

// 手动触发表单验证
const handleSubmit = () => {
  formRef.value?.submit()
}

// 提交表单
const onSubmit = async (context) => {
  const { validateResult, firstError } = context
  if (validateResult !== true) {
    MessagePlugin.error(firstError)
    return
  }

  try {
    submitting.value = true
    const { post } = useHttp()
    const url = isEdit.value ? '/api/news/update' : '/api/news/save'
    await post(url, formData.value)
    
    MessagePlugin.success(isEdit.value ? '更新成功' : '创建成功')
    formData.value = {
      id: null,
      title: '',
      content: '',
    }
    
    // 清空编辑器内容
    if (quillEditor.value && quillEditor.value.setHTML) {
      quillEditor.value.setHTML('')
    }
    
    drawerVisible.value = false
    fetchNews()
  } catch (error) {
    MessagePlugin.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchNews()
})
</script>

<style scoped>
.news-container {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.content-preview {
  max-height: 60px;
  overflow: hidden;
  line-height: 1.4;
  color: #666;
}

.editor-container {
  border: 1px solid #d0d7de;
  border-radius: 6px;
  overflow: hidden;
}

.editor-container :deep(.ql-toolbar.ql-snow) {
  border-bottom: 1px solid #d0d7de;
  background-color: #f6f8fa;
}

.editor-container :deep(.ql-container.ql-snow) {
  border: none;
  font-family: inherit;
}

.editor-container :deep(.ql-editor) {
  min-height: 250px;
  font-size: 14px;
  line-height: 1.6;
}
</style> 