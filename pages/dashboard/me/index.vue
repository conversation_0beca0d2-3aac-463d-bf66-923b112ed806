<template>
    <div class="change-password-container">
        <t-card title="修改密码" class="mt-4">
            <t-form ref="form" :data="formData" :rules="rules" @submit="onSubmit" label-align="top" class="w-96">
                <t-form-item label="当前密码" name="oldPassword">
                    <t-input v-model="formData.oldPassword" type="password" placeholder="请输入当前密码" />
                </t-form-item>
                <t-form-item label="新密码" name="newPassword">
                    <t-input v-model="formData.newPassword" type="password" placeholder="请输入新密码" />
                </t-form-item>
                <t-form-item label="确认新密码" name="confirmPassword">
                    <t-input v-model="formData.confirmPassword" type="password" placeholder="请再次输入新密码" />
                </t-form-item>
                <t-form-item>
                    <t-space>
                        <t-button theme="primary" type="submit">确认修改</t-button>
                        <t-button theme="default" variant="base" @click="onReset">重置</t-button>
                    </t-space>
                </t-form-item>
            </t-form>
        </t-card>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

definePageMeta({
    layout: 'dashboard'
})

const formData = ref({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
})

const rules = {
    oldPassword: [
        { required: true, message: '请输入当前密码', type: 'error' },
        { min: 6, message: '密码长度至少为6位', type: 'error' }
    ],
    newPassword: [
        { required: true, message: '请输入新密码', type: 'error' },
        { min: 6, message: '密码长度至少为6位', type: 'error' }
    ],
    confirmPassword: [
        { required: true, message: '请确认新密码', type: 'error' },
        { validator: (val) => val === formData.value.newPassword, message: '两次输入的密码不一致', type: 'error' }
    ]
}

const form = ref(null)

const onSubmit = async ({ validateResult, firstError }) => {
    if (validateResult == true) {
        const { post } = useHttp();
        await post("/api/user/change_password", formData.value);
        MessagePlugin.success('密码修改成功')

        const { logout } = useAuth();
        await logout();
    } else {
        MessagePlugin.error(firstError)
    }

}

const onReset = () => {
    form.value?.reset()
}
</script>

<style scoped>
.change-password-container {
    padding: 20px;
}
</style>
