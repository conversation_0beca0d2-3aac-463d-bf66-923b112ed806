<template>
  <div class="roles-container">
    <t-card title="角色管理">
      <template #actions>
        <has-permission permission="role:create">
          <t-button theme="primary" @click="handleAddRole">
            <template #icon>
              <t-icon name="add" />
            </template>
            新增角色
          </t-button>
        </has-permission>
      </template>

                      <t-table :data="roles" :columns="columns" :loading="loading" :pagination="pagination" row-key="id"
                    @page-change="onPageChange">
        <template #permissions="{ row }">
          <t-tag v-for="permission in row.permissions" :key="permission" theme="primary" variant="light"
            style="margin: 2px">
            {{ permission }}
          </t-tag>
        </template>

        <template #op="slotProps">
          <t-space>
            <has-permission permission="role:edit">
              <t-button theme="primary" variant="text" @click="handleEdit(slotProps.row)">
                编辑
              </t-button>
            </has-permission>
            <has-permission permission="role:delete">
              <t-button theme="danger" variant="text" @click="handleDelete(slotProps.row)">
                删除
              </t-button>
            </has-permission>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 新增/编辑角色抽屉 -->
    <ClientOnly>
      <t-drawer v-model:visible="drawerVisible" :size="drawerSize" :header="drawerTitle" :footer="true"
        @close="onDrawerClose">
        <template #body>
          <t-form ref="form" :data="formData" :rules="rules" @submit="onSubmit" label-align="top" class="role-form">
            <t-form-item label="角色名称" name="name">
              <t-input v-model="formData.name" placeholder="请输入角色名称" />
            </t-form-item>

            <t-form-item label="权限设置" name="permissionIds">
              <div class="permissions-container">
                <div class="permissions-header">
                  <t-checkbox :checked="isAllSelected" :indeterminate="isIndeterminate" @change="handleSelectAll">
                    全选/取消全选
                  </t-checkbox>
                  <div class="permission-tips">
                    <t-icon name="info-circle" />
                    <span>选择菜单权限控制菜单显示，选择按钮权限控制功能操作</span>
                  </div>
                </div>
                
                <div class="permissions-tree-container">
                  <div class="tree-scroll-area">
                    <div v-for="menuPermission in menuPermissions" :key="menuPermission.id" class="menu-permission-node">
                      <!-- 菜单权限节点 -->
                      <div class="menu-node">
                        <t-checkbox 
                          :value="menuPermission.id" 
                          :checked="formData.permissionIds.includes(menuPermission.id)"
                          @change="(checked) => handlePermissionChange(menuPermission.id, checked)"
                          class="menu-checkbox">
                          <div class="menu-content">
                            <t-icon :name="getMenuIcon(menuPermission.code)" class="menu-icon" />
                            <span class="menu-name">{{ menuPermission.name }}</span>
                            <span class="menu-desc">{{ menuPermission.description }}</span>
                          </div>
                        </t-checkbox>
                      </div>
                      
                      <!-- 子菜单权限 -->
                      <div v-if="menuPermission.children && menuPermission.children.length > 0" class="submenu-nodes">
                        <div v-for="submenu in menuPermission.children" :key="submenu.id" class="submenu-node">
                          <div class="submenu-header">
                            <t-checkbox 
                              :value="submenu.id" 
                              :checked="formData.permissionIds.includes(submenu.id)"
                              @change="(checked) => handlePermissionChange(submenu.id, checked)"
                              class="submenu-checkbox">
                              <span class="submenu-name">{{ submenu.name }}</span>
                            </t-checkbox>
                          </div>
                          
                          <!-- 该子菜单下的按钮权限 -->
                          <div v-if="submenu.buttonPermissions && submenu.buttonPermissions.length > 0" class="button-permissions">
                            <div class="button-permissions-header">
                              <span class="button-section-title">功能权限</span>
                              <t-checkbox 
                                :checked="isSubmenuButtonsSelected(submenu.buttonPermissions)"
                                :indeterminate="isSubmenuButtonsIndeterminate(submenu.buttonPermissions)"
                                @change="(checked) => handleSubmenuButtonsSelect(submenu.buttonPermissions, checked)"
                                class="button-group-checkbox">
                                全选
                              </t-checkbox>
                            </div>
                            <div class="button-permissions-list">
                              <div v-for="buttonPerm in submenu.buttonPermissions" :key="buttonPerm.id" class="button-permission-item">
                                <t-checkbox 
                                  :value="buttonPerm.id" 
                                  :checked="formData.permissionIds.includes(buttonPerm.id)"
                                  @change="(checked) => handlePermissionChange(buttonPerm.id, checked)"
                                  class="button-permission-checkbox">
                                  <div class="button-permission-content">
                                    <span class="button-permission-name">{{ buttonPerm.name }}</span>
                                    <span class="button-permission-desc">{{ buttonPerm.description }}</span>
                                  </div>
                                </t-checkbox>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <!-- 直接在主菜单下的按钮权限 -->
                      <div v-if="menuPermission.buttonPermissions && menuPermission.buttonPermissions.length > 0" class="button-permissions">
                        <div class="button-permissions-header">
                          <span class="button-section-title">功能权限</span>
                          <t-checkbox 
                            :checked="isSubmenuButtonsSelected(menuPermission.buttonPermissions)"
                            :indeterminate="isSubmenuButtonsIndeterminate(menuPermission.buttonPermissions)"
                            @change="(checked) => handleSubmenuButtonsSelect(menuPermission.buttonPermissions, checked)"
                            class="button-group-checkbox">
                            全选
                          </t-checkbox>
                        </div>
                        <div class="button-permissions-list">
                          <div v-for="buttonPerm in menuPermission.buttonPermissions" :key="buttonPerm.id" class="button-permission-item">
                            <t-checkbox 
                              :value="buttonPerm.id" 
                              :checked="formData.permissionIds.includes(buttonPerm.id)"
                              @change="(checked) => handlePermissionChange(buttonPerm.id, checked)"
                              class="button-permission-checkbox">
                              <div class="button-permission-content">
                                <span class="button-permission-name">{{ buttonPerm.name }}</span>
                                <span class="button-permission-desc">{{ buttonPerm.description }}</span>
                              </div>
                            </t-checkbox>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </t-form-item>
          </t-form>
        </template>

        <template #footer>
          <t-space>
            <t-button theme="default" variant="base" @click="onDrawerClose">取消</t-button>
            <t-button theme="primary" @click="onSubmit" :loading="submitting">确定</t-button>
          </t-space>
        </template>
      </t-drawer>
    </ClientOnly>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';

definePageMeta({
  layout: 'dashboard'
});

const roles = ref([]);
const loading = ref(false);
const pagination = ref({
  total: 0,
  pageSize: 10,
  current: 1,
});

// 抽屉相关
const drawerVisible = ref(false);
const drawerSize = '1000px';
const drawerTitle = ref('新增角色');
const submitting = ref(false);

// 表单数据
const formData = ref({
  id: null,
  name: '',
  permissionIds: []
});

// 表单规则
const rules = {
  name: [{ required: true, message: '请输入角色名称', type: 'error' }],
  permissionIds: [{ required: true, message: '请选择至少一个权限', type: 'error' }]
};

// 权限数据
const groupedPermissions = ref({});
const permissionsTree = ref([]);

// 树形菜单权限数据
const menuPermissions = computed(() => {
  // 直接使用从API获取的树形数据
  return permissionsTree.value;
});

// 根据权限代码获取菜单图标
const getMenuIcon = (code) => {
  const iconMap = {
    'dashboard': 'dashboard',
    'order:menu': 'layers',
    'finance:menu': 'saving-pot',
    'supplier:menu': 'cooperate',
    'customer:menu': 'member',
    'fleet:menu': 'vehicle',
    'product:menu': 'server',
    'system:menu': 'setting',
    'user:menu': 'user',
    'role:menu': 'user-setting',
    'log:menu': 'file-text'
  };
  return iconMap[code] || 'folder';
};

// 处理单个权限的选择/取消选择
const handlePermissionChange = (permissionId, checked) => {
  if (checked) {
    // 添加权限
    if (!formData.value.permissionIds.includes(permissionId)) {
      formData.value.permissionIds.push(permissionId);
    }
  } else {
    // 移除权限
    formData.value.permissionIds = formData.value.permissionIds.filter(id => id !== permissionId);
  }
};

// 判断子菜单的按钮权限是否全选
const isSubmenuButtonsSelected = (buttonPermissions) => {
  if (!buttonPermissions || buttonPermissions.length === 0) return false;
  return buttonPermissions.every(p => formData.value.permissionIds.includes(p.id));
};

// 判断子菜单的按钮权限是否半选
const isSubmenuButtonsIndeterminate = (buttonPermissions) => {
  if (!buttonPermissions || buttonPermissions.length === 0) return false;
  const selectedCount = buttonPermissions.filter(p => formData.value.permissionIds.includes(p.id)).length;
  return selectedCount > 0 && selectedCount < buttonPermissions.length;
};

// 处理子菜单按钮权限的全选/取消全选
const handleSubmenuButtonsSelect = (buttonPermissions, checked) => {
  const buttonIds = buttonPermissions.map(p => p.id);
  if (checked) {
    // 添加所有按钮权限
    const newIds = [...formData.value.permissionIds];
    buttonIds.forEach(id => {
      if (!newIds.includes(id)) {
        newIds.push(id);
      }
    });
    formData.value.permissionIds = newIds;
  } else {
    // 移除所有按钮权限
    formData.value.permissionIds = formData.value.permissionIds.filter(id =>
      !buttonIds.includes(id)
    );
  }
};

// 全选相关计算属性（适应新的树形权限结构）
const allPermissionIds = computed(() => {
  const ids = [];
  
  const collectIds = (permissions) => {
    permissions.forEach(permission => {
      ids.push(permission.id);
      if (permission.children) {
        collectIds(permission.children);
      }
      if (permission.buttonPermissions) {
        permission.buttonPermissions.forEach(bp => ids.push(bp.id));
      }
    });
  };
  
  collectIds(menuPermissions.value);
  return ids;
});

const isAllSelected = computed(() => {
  return allPermissionIds.value.length > 0 &&
    allPermissionIds.value.every(id => formData.value.permissionIds.includes(id));
});

const isIndeterminate = computed(() => {
  const selectedCount = allPermissionIds.value.filter(id =>
    formData.value.permissionIds.includes(id)
  ).length;
  return selectedCount > 0 && selectedCount < allPermissionIds.value.length;
});

// 全选/取消全选
const handleSelectAll = (checked) => {
  if (checked) {
    formData.value.permissionIds = [...allPermissionIds.value];
  } else {
    formData.value.permissionIds = [];
  }
};

// 判断分组是否全选
const isGroupSelected = (permissions) => {
  return permissions.length > 0 &&
    permissions.every(permission => formData.value.permissionIds.includes(permission.id));
};

// 判断分组是否半选
const isGroupIndeterminate = (permissions) => {
  const selectedCount = permissions.filter(permission =>
    formData.value.permissionIds.includes(permission.id)
  ).length;
  return selectedCount > 0 && selectedCount < permissions.length;
};

// 分组全选/取消全选
const handleGroupSelect = (permissions, checked) => {
  const permissionIds = permissions.map(p => p.id);
  if (checked) {
    // 添加该组所有权限
    const newIds = [...formData.value.permissionIds];
    permissionIds.forEach(id => {
      if (!newIds.includes(id)) {
        newIds.push(id);
      }
    });
    formData.value.permissionIds = newIds;
  } else {
    // 移除该组所有权限
    formData.value.permissionIds = formData.value.permissionIds.filter(id =>
      !permissionIds.includes(id)
    );
  }
};

// 获取分组图标
const getGroupIcon = (group) => {
  const iconMap = {
    '订单管理': 'layers',
    '客户管理': 'member',
    '供应商管理': 'cooperate',
    '产品管理': 'server',
    '车队管理': 'vehicle',
    '交易管理': 'saving-pot',
    '系统管理': 'setting'
  };
  return iconMap[group] || 'folder';
};

// 获取分组描述
const getGroupDescription = (group) => {
  const descMap = {
    '订单管理': '订单列表、新增订单、导出等功能',
    '客户管理': '客户列表、客户信息管理、客户财务等',
    '供应商管理': '供应商列表、供应商信息管理、供应商财务等',
    '产品管理': '产品信息的查看和管理',
    '车队管理': '车队信息、车辆管理等功能',
    '交易管理': '财务统计、交易流水等功能',
    '系统管理': '用户管理、角色管理、系统设置等'
  };
  return descMap[group] || '';
};

// 获取权限描述
const getPermissionDescription = (code) => {
  const descMap = {
    // 订单管理
    'order:view': '可查看订单列表，访问"订单管理"菜单',
    'order:create': '可新增订单，访问"新增订单"页面',
    'order:edit': '可编辑订单信息',
    'order:delete': '可删除订单',
    'order:export': '可导出订单数据到Excel',
    
    // 客户管理
    'customer:view': '可查看客户列表，访问"客户管理"菜单',
    'customer:create': '可新增客户信息',
    'customer:edit': '可编辑客户信息',
    'customer:delete': '可删除客户',
    
    // 供应商管理
    'supplier:view': '可查看供应商列表，访问"供应商管理"菜单',
    'supplier:create': '可新增供应商信息',
    'supplier:edit': '可编辑供应商信息',
    'supplier:delete': '可删除供应商',
    
    // 产品管理
    'product:view': '可查看产品列表，访问"产品管理"菜单',
    'product:create': '可新增产品信息',
    'product:edit': '可编辑产品信息',
    'product:delete': '可删除产品',
    
    // 车队管理
    'fleet:view': '可查看车队信息，访问"车队管理"菜单',
    'fleet:create': '可新增车队和车辆信息',
    'fleet:edit': '可编辑车队和车辆信息',
    'fleet:delete': '可删除车队和车辆',
    
    // 交易管理
    'transaction:view': '可查看财务统计，访问"财务管理"菜单',
    'transaction:create': '可新增交易记录',
    'transaction:edit': '可编辑交易记录',
    'transaction:delete': '可删除交易记录',
    
    // 系统管理
    'user:view': '可查看用户列表，访问"用户列表"页面',
    'user:create': '可新增用户',
    'user:edit': '可编辑用户信息',
    'user:delete': '可删除用户',
    'user:reset-password': '可重置用户密码',
    'user:change_password': '可修改自己的密码',
    
    'role:view': '可查看角色列表，访问"角色管理"页面',
    'role:create': '可新增角色',
    'role:edit': '可编辑角色权限',
    'role:delete': '可删除角色',
    
    'log:view': '可查看操作日志，访问"操作日志"页面'
  };
  return descMap[code] || '执行相关操作的权限';
};

// 获取操作权限描述（更简洁的描述）
const getActionPermissionDescription = (code) => {
  const descMap = {
    // 订单管理
    'order:create': '新增订单',
    'order:edit': '编辑订单信息',
    'order:delete': '删除订单',
    'order:export': '导出订单数据',
    
    // 客户管理
    'customer:create': '新增客户',
    'customer:edit': '编辑客户信息',
    'customer:delete': '删除客户',
    
    // 供应商管理
    'supplier:create': '新增供应商',
    'supplier:edit': '编辑供应商信息',
    'supplier:delete': '删除供应商',
    
    // 产品管理
    'product:create': '新增产品',
    'product:edit': '编辑产品信息',
    'product:delete': '删除产品',
    
    // 车队管理
    'fleet:create': '新增车队和车辆',
    'fleet:edit': '编辑车队和车辆信息',
    'fleet:delete': '删除车队和车辆',
    
    // 交易管理
    'transaction:create': '新增交易记录',
    'transaction:edit': '编辑交易记录',
    'transaction:delete': '删除交易记录',
    
    // 系统管理
    'user:create': '新增用户',
    'user:edit': '编辑用户信息',
    'user:delete': '删除用户',
    'user:reset-password': '重置用户密码',
    
    'role:create': '新增角色',
    'role:edit': '编辑角色权限',
    'role:delete': '删除角色'
  };
  return descMap[code] || '执行相关操作';
};

// 页面加载时获取角色列表
const fetchRoles = async () => {
  loading.value = true;
  try {
    const { post } = useHttp();
    const response = await post('/api/sys/roles/list');
    roles.value = response;
    pagination.value.total = response.length;
  } catch (error) {
    MessagePlugin.error('获取角色列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取权限列表
const fetchPermissions = async () => {
  try {
    const { post } = useHttp();
    const response = await post('/api/sys/permissions/list');
    // 处理新的API数据结构
    if (response && response.tree) {
      permissionsTree.value = response.tree;
      groupedPermissions.value = response.grouped || {};
    } else {
      // 兼容旧的数据结构
      groupedPermissions.value = response || {};
      permissionsTree.value = [];
    }
  } catch (error) {
    MessagePlugin.error('获取权限列表失败');
    console.error('获取权限失败:', error);
  }
};

// 页码变化
const onPageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchRoles();
};

// 添加角色
const handleAddRole = async () => {
  isEdit.value = false;
  drawerTitle.value = '新增角色';
  formData.value = {
    id: null,
    name: '',
    permissionIds: []
  };
  await fetchPermissions();
  drawerVisible.value = true;
};

// 编辑角色
const handleEdit = async (role) => {
  isEdit.value = true;
  drawerTitle.value = '编辑角色';

  // 获取角色的权限ID列表
  const { post } = useHttp();
  // 获取权限列表
  await fetchPermissions();

  // 获取角色详情
  const result = await post('/api/sys/roles/detail', { id: role.id });

  formData.value = {
    id: result.id,
    name: result.name,
    permissionIds: result.permissionIds || []
  };
  drawerVisible.value = true;

};

// 删除角色
const handleDelete = (role) => {
  // 禁止删除管理员角色(id=1)
  if (role.id === 1) {
    MessagePlugin.warning('系统管理员角色不能删除');
    return;
  }

  const { post } = useHttp();

  // 使用 Dialog 显示确认对话框
  const confirm = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除角色"${role.name}"吗？删除后数据不可恢复！`,
    theme: 'danger',
    confirmBtn: {
      content: '确认删除',
      theme: 'danger',
    },
    cancelBtn: {
      content: '取消',
      theme: 'default',
    },
    onConfirm: async () => {
      await post('/api/sys/roles/delete', { id: role.id });
      confirm.hide();
      MessagePlugin.success('删除成功');
      fetchRoles();
    }
  });
};

// 关闭抽屉
const onDrawerClose = () => {
  drawerVisible.value = false;
  formData.value = {
    id: null,
    name: '',
    permissionIds: []
  };
  isEdit.value = false;
};

// 提交表单
const onSubmit = async () => {
  if (!formData.value.name || formData.value.permissionIds.length === 0) {
    MessagePlugin.error('请填写完整信息');
    return;
  }

  submitting.value = true;
  try {
    const { post } = useHttp();
    const url = isEdit.value ? '/api/sys/roles/update' : '/api/sys/roles/save';
    await post(url, formData.value);
    MessagePlugin.success(isEdit.value ? '角色更新成功' : '角色创建成功');
    drawerVisible.value = false;
    fetchRoles();
  } catch (error) {
    MessagePlugin.error(isEdit.value ? '角色更新失败' : '角色创建失败');
  } finally {
    submitting.value = false;
  }
};

// 当前是否为编辑模式
const isEdit = ref(false);

// 定义表格列
const columns = [
  {
    colKey: 'name',
    title: '角色名称',
    width: 200,
  },
  {
    colKey: 'permissions',
    title: '权限列表',
    cell: 'permissions',
  },
  {
    colKey: 'op',
    title: '操作',
    width: 160,
    cell: 'op',
  },
];

// 页面加载时获取角色列表
onMounted(() => {
  fetchRoles();
});
</script>

<style scoped>
.roles-container {
  padding: 20px;
}

.permissions-container {
  flex: 1;
  min-height: 400px;
  max-height: calc(100vh - 280px);
  display: flex;
  flex-direction: column;
}

.permissions-header {
  flex-shrink: 0;
  margin-bottom: 16px;
  padding: 12px;
  background-color: var(--td-bg-color-secondarycontainer);
  border-radius: 6px;
  border: 1px solid var(--td-component-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.permission-tips {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--td-text-color-placeholder);
}

.permissions-header :deep(.t-checkbox) {
  font-weight: 600;
}

.permissions-tree-container {
  flex: 1;
  border: 1px solid var(--td-component-border);
  border-radius: 6px;
  overflow: hidden;
  background-color: var(--td-bg-color-container);
  position: relative;
}

.tree-scroll-area {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  max-height: calc(100vh - 350px);
}

.tree-scroll-area::-webkit-scrollbar {
  width: 6px;
}

.tree-scroll-area::-webkit-scrollbar-track {
  background: var(--td-bg-color-container);
  border-radius: 3px;
}

.tree-scroll-area::-webkit-scrollbar-thumb {
  background: var(--td-bg-color-component-hover);
  border-radius: 3px;
}

.menu-permission-node {
  margin-bottom: 24px;
  border: 1px solid var(--td-component-border);
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--td-bg-color-container);
}

.menu-node {
  padding: 12px 16px;
  background-color: var(--td-bg-color-container-hover);
  border-bottom: 1px solid var(--td-component-border);
}

.menu-checkbox {
  width: 100%;
}

.menu-checkbox :deep(.t-checkbox__input) {
  pointer-events: auto;
}

.menu-checkbox :deep(.t-checkbox__label) {
  pointer-events: none;
  width: 100%;
}

.menu-content {
  display: flex;
  align-items: center;
  gap: 8px;
  pointer-events: none;
}

.menu-icon {
  color: var(--td-brand-color);
  font-size: 16px;
}

.menu-name {
  font-weight: 600;
  font-size: 14px;
  color: var(--td-text-color-primary);
}

.menu-desc {
  font-size: 12px;
  color: var(--td-text-color-placeholder);
  margin-left: 8px;
}

.submenu-nodes {
  border-bottom: 1px solid var(--td-component-border);
}

.submenu-node {
  border-bottom: 1px solid var(--td-component-border);
}

.submenu-node:last-child {
  border-bottom: none;
}

.submenu-header {
  padding: 10px 16px;
  background-color: var(--td-bg-color-container-active);
}

.submenu-checkbox {
  width: 100%;
}

.submenu-checkbox :deep(.t-checkbox__input) {
  pointer-events: auto;
}

.submenu-checkbox :deep(.t-checkbox__label) {
  pointer-events: none;
}

.submenu-name {
  font-weight: 500;
  font-size: 13px;
  color: var(--td-text-color-primary);
  pointer-events: none;
}

.button-permissions {
  padding: 12px 16px;
}

.button-permissions-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--td-component-border);
}

.button-section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--td-text-color-secondary);
}

.button-group-checkbox {
  font-size: 12px;
}

.button-group-checkbox :deep(.t-checkbox__input) {
  pointer-events: auto;
}

.button-permissions-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.button-permission-item {
  border: 1px solid var(--td-component-border);
  border-radius: 4px;
  transition: all 0.2s;
  background-color: var(--td-bg-color-container);
}

.button-permission-item:hover {
  border-color: var(--td-brand-color);
  background-color: var(--td-bg-color-container-hover);
}

.button-permission-checkbox {
  width: 100%;
  padding: 8px;
  margin: 0;
}

.button-permission-checkbox :deep(.t-checkbox__input) {
  pointer-events: auto;
}

.button-permission-checkbox :deep(.t-checkbox__label) {
  width: 100%;
  pointer-events: none;
}

.button-permission-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
  pointer-events: none;
}

.button-permission-name {
  font-weight: 500;
  font-size: 12px;
  color: var(--td-text-color-primary);
}

.button-permission-desc {
  font-size: 11px;
  color: var(--td-text-color-placeholder);
  line-height: 1.3;
}

.role-form {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.role-form :deep(.t-form-item:last-child) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.role-form :deep(.t-form-item:last-child .t-form__controls) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button-permissions-list {
    grid-template-columns: 1fr;
  }
  
  .menu-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .menu-desc {
    margin-left: 0;
  }
}
</style>
