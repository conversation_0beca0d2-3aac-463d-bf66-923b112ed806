<template>
    <div class="log-container">
        <client-only>
            <t-card title="操作日志">
                <template #actions>
                    <t-space>
                        <t-select v-model="searchModule" placeholder="选择模块" :options="moduleOptions" clearable />
                        <t-button theme="primary" @click="fetchLogs">
                            <template #icon>
                                <t-icon name="search" />
                            </template>
                            查询
                        </t-button>
                    </t-space>
                </template>

                            <t-table :data="logs" :columns="columns" :loading="loading" :pagination="pagination" row-key="id"
                @page-change="onPageChange">
                    <template #module="{ row }">
                        <t-tag theme="primary" variant="light">
                            {{ getModuleName(row.module) }}
                        </t-tag>
                    </template>

                    <template #action="{ row }">
                        <t-tag :theme="getActionTheme(row.action)" variant="light">
                            {{ getActionName(row.action) }}
                        </t-tag>
                    </template>

                    <template #data="{ row }">
                        <t-link theme="primary" @click="showDataDetail(row)">查看详情</t-link>
                    </template>

                    <template #createTime="{ row }">
                        {{ formatDate(row.createdAt) }}
                    </template>
                </t-table>
            </t-card>
        </client-only>

        <client-only>
            <!-- 数据详情对话框 -->
            <t-dialog v-model:visible="dialogVisible" header="数据详情" :width="900" :max-height="700">
                <div class="data-detail-container">
                    <template v-if="formattedData.length > 0">
                        <div class="data-section">
                            <div class="section-header">
                                <t-icon name="view-list" class="section-icon" />
                                数据变更详情
                            </div>
                            <t-table 
                                :data="formattedData" 
                                :columns="detailColumns" 
                                row-key="field"
                                size="small"
                                bordered
                                :max-height="400"
                            >
                                <template #fieldName="{ row }">
                                    <span class="field-label">{{ row.fieldName }}</span>
                                </template>
                                
                                <template #oldValue="{ row }">
                                    <span class="old-value">{{ row.oldValue || '-' }}</span>
                                </template>
                                
                                <template #newValue="{ row }">
                                    <span :class="['new-value', { 'changed': row.isChanged }]">
                                        {{ row.newValue || '-' }}
                                    </span>
                                </template>
                                
                                <template #status="{ row }">
                                    <t-tag 
                                        v-if="row.isChanged" 
                                        theme="warning" 
                                        variant="light" 
                                        size="small"
                                    >
                                        已修改
                                    </t-tag>
                                    <t-tag 
                                        v-else-if="row.isNew" 
                                        theme="success" 
                                        variant="light" 
                                        size="small"
                                    >
                                        新增
                                    </t-tag>
                                    <span v-else class="unchanged">未变更</span>
                                </template>
                            </t-table>
                        </div>
                        
                        <!-- 原始JSON数据（可展开查看） -->
                        <div class="raw-data-section">
                            <t-collapse>
                                <t-collapse-panel header="查看原始JSON数据" value="raw">
                                    <div class="json-container">
                                        <template v-if="selectedLog?.oldData">
                                            <div class="json-section">
                                                <strong>修改前数据：</strong>
                                                <pre class="json-content">{{ selectedLog.oldData }}</pre>
                                            </div>
                                        </template>
                                        
                                        <template v-if="selectedLog?.newData">
                                            <div class="json-section">
                                                <strong>修改后数据：</strong>
                                                <pre class="json-content">{{ selectedLog.newData }}</pre>
                                            </div>
                                        </template>
                                    </div>
                                </t-collapse-panel>
                            </t-collapse>
                        </div>
                    </template>

                    <template v-else>
                        <div class="empty-data">
                            <t-icon name="info-circle" size="48px" />
                            <p>暂无详细数据</p>
                        </div>
                    </template>
                </div>
                <template #footer>
                    <t-space>
                        <t-button theme="default" @click="dialogVisible = false">关闭</t-button>
                        <t-button theme="primary" @click="copyAllData">复制数据</t-button>
                    </t-space>
                </template>
            </t-dialog>
        </client-only>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { formatDate } from '~/utils/date';

definePageMeta({
    layout: 'dashboard'
});

// 模块选项
const moduleOptions = [
    { label: '订单管理', value: 'order' },
    { label: '客户管理', value: 'customer' },
    { label: '供应商管理', value: 'supplier' },
    { label: '产品管理', value: 'product' },
    { label: '车队管理', value: 'fleet' },
    { label: '交易管理', value: 'transaction' },
    { label: '用户管理', value: 'user' },
    { label: '角色管理', value: 'role' },
    { label: '权限管理', value: 'permission' },
    { label: '操作日志', value: 'log' },
    { label: '银行卡管理', value: 'bank_card' },
    { label: '银行卡流水', value: 'bank_card_flow' },
];

// 获取模块名称
const getModuleName = (module: string) => {
    const option = moduleOptions.find(opt => opt.value === module);
    return option?.label || module;
};

// 操作类型主题
const actionThemes: Record<string, 'success' | 'warning' | 'danger' | 'default'> = {
    create: 'success',
    update: 'warning', 
    delete: 'danger',
    batch_create: 'success',
    reset_password: 'warning',
    change_password: 'warning',
    create_vehicle: 'success',
    update_vehicle: 'warning',
    delete_vehicle: 'danger',
};

// 获取操作类型主题
const getActionTheme = (action: string) => {
    return actionThemes[action] || 'default';
};

// 获取操作类型名称
const getActionName = (action: string) => {
    const actionNames = {
        create: '新增',
        update: '修改',
        delete: '删除',
        batch_create: '批量导入',
        reset_password: '重置密码',
        change_password: '修改密码',
        create_vehicle: '新增车辆',
        update_vehicle: '修改车辆',
        delete_vehicle: '删除车辆',
    };
    return actionNames[action as keyof typeof actionNames] || action;
};

const logs = ref([]);
const loading = ref(false);
const pagination = ref({
    total: 0,
    pageSize: 10,
    current: 1,
});
const searchModule = ref('');

// 对话框相关
const dialogVisible = ref(false);
const selectedLog = ref<any>(null);

// 格式化数据
const formattedData = ref<any[]>([]);

// 详情表格列定义
const detailColumns = [
    {
        colKey: 'fieldName',
        title: '字段名称',
        width: 200,
        cell: 'fieldName',
    },
    {
        colKey: 'oldValue',
        title: '修改前',
        width: 200,
        cell: 'oldValue',
    },
    {
        colKey: 'newValue',
        title: '修改后', 
        width: 200,
        cell: 'newValue',
    },
    {
        colKey: 'status',
        title: '状态',
        width: 100,
        cell: 'status',
    },
];

// 字段名翻译映射
const fieldNameMap: Record<string, string> = {
    // 通用字段
    'id': 'ID',
    'name': '名称',
    'status': '状态',
    'createdAt': '创建时间',
    'updatedAt': '更新时间',
    'remark': '备注',
    
    // 订单相关
    'orderNumber': '派车单号',
    'customerId': '客户ID',
    'customerName': '客户名称',
    'customerPhone': '客户电话',
    'supplierId': '材料账号ID',
    'supplierName': '材料账号名称',
    'supplierType': '材料账号类型',
    'shipper': '发货单位',
    'productId': '产品ID',
    'productName': '产品名称',
    'productSerialNumber': '产品批号',
    'fleetId': '车队ID',
    'fleetName': '车队名称',
    'fleetVehiclesId': '车辆ID',
    'licensePlate': '车牌号',
    'driver': '司机',
    'driverPhone': '司机电话',
    'grossWeight': '毛重(吨)',
    'tareWeight': '皮重(吨)',
    'netWeight': '过磅净重(吨)',
    'actualNetWeight': '实际净重(吨)',
    'price': '单价',
    'totalPrice': '总价',
    'unloadWeight': '卸车吨数',
    'unloadPrice': '卸车单价',
    'unloadTotalPrice': '卸车总价',
    'freightPrice': '运费单价',
    'freightTotalPrice': '运费总价',
    'inFactoryTime': '入厂时间',
    'outFactoryTime': '出厂时间',
    'unloadDate': '卸车日期',
    
    // 用户相关
    'username': '用户名',
    'phone': '手机号',
    'roleId': '角色ID',
    'roleName': '角色名称',
    'password': '密码',
    
    // 角色相关
    'permissions': '权限列表',
    'description': '描述',
    
    // 客户相关
    'contact': '联系人',
    'address': '地址',
    
    // 供应商相关
    'type': '材料账号类型',
    
    // 车队相关
    'vehicles': '车辆列表',
    
    // 交易相关
    'amount': '金额',
    'transactionType': '交易类型',
    'paymentMethod': '支付方式',
    'bankCardId': '银行卡ID',
    'bankCardFlowId': '银行卡流水ID',
    'balance': '余额',
    
    // 银行卡相关
    'bankName': '银行名称',
    'bankCardName': '银行卡名称',
    'cardNumber': '卡号',
    'inout': '收支类型',
    'accountName': '账户名称',
    'initialBalance': '初始余额',
    'currentBalance': '当前余额',
};

// 格式化字段值
const formatFieldValue = (key: string, value: any): string => {
    if (value === null || value === undefined) return '-';
    
    // 状态字段
    if (key === 'status') {
        const statusMap: Record<number, string> = {
            1: '启用',
            2: '禁用', 
            3: '删除'
        };
        return statusMap[value] || String(value);
    }
    
    // 时间字段
    if (key.includes('Time') || key.includes('Date') || key === 'createdAt' || key === 'updatedAt') {
        return formatDate(value);
    }
    
    // 金额字段
    if (key.includes('Price') || key.includes('Weight') || key === 'amount' || key === 'balance') {
        const num = parseFloat(value);
        return isNaN(num) ? String(value) : num.toFixed(2);
    }
    
    // 数组字段
    if (Array.isArray(value)) {
        return value.length > 0 ? `${value.length}项` : '无';
    }
    
    return String(value);
};

// 处理数据详情显示
const processDataForDisplay = (oldData: any, newData: any) => {
    const result: any[] = [];
    const allFields = new Set([
        ...Object.keys(oldData || {}),
        ...Object.keys(newData || {})
    ]);
    
    for (const field of allFields) {
        const oldValue = oldData?.[field];
        const newValue = newData?.[field];
        const isChanged = oldValue !== newValue;
        const isNew = oldData && !oldData.hasOwnProperty(field) && newData?.hasOwnProperty(field);
        
        result.push({
            field,
            fieldName: fieldNameMap[field] || field,
            oldValue: formatFieldValue(field, oldValue),
            newValue: formatFieldValue(field, newValue),
            isChanged,
            isNew,
        });
    }
    
    return result.sort((a, b) => {
        // 已修改的排在前面
        if (a.isChanged && !b.isChanged) return -1;
        if (!a.isChanged && b.isChanged) return 1;
        // 新增的排在已修改后面
        if (a.isNew && !b.isNew) return -1;
        if (!a.isNew && b.isNew) return 1;
        // 其他按字段名排序
        return a.fieldName.localeCompare(b.fieldName);
    });
};

// 定义表格列
const columns = [
    {
        colKey: 'module',
        title: '模块',
        width: 120,
        cell: 'module',
    },
    {
        colKey: 'action',
        title: '操作类型',
        width: 100,
        cell: 'action',
    },
    {
        colKey: 'operatorName',
        title: '操作人',
        width: 120,
    },
    {
        colKey: 'ip',
        title: 'IP地址',
        width: 140,
    },
    {
        colKey: 'data',
        title: '操作数据',
        width: 100,
        cell: 'data',
    },
    {
        colKey: 'createdAt',
        title: '操作时间',
        width: 180,
        cell: 'createTime',
    },
];

// 格式化日期
/* const formatDate = (date: Date | string | null) => {
    if (!date) return '-';
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}`
}; */

// 获取日志列表
const fetchLogs = async () => {
    loading.value = true;
    try {
        const { post } = useHttp();
        const response = await post('/api/sys/logs/list', {
            pageSize: pagination.value.pageSize,
            current: pagination.value.current,
            module: searchModule.value || undefined,
        });
        logs.value = response.list;
        pagination.value.total = response.total;
    } finally {
        loading.value = false;
    }
};

// 页码变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
    pagination.value.current = pageInfo.current;
    pagination.value.pageSize = pageInfo.pageSize;
    fetchLogs();
};

// 显示数据详情
const showDataDetail = (log: any) => {
    try {
        const oldData = log.oldData ? JSON.parse(log.oldData) : null;
        const newData = log.newData ? JSON.parse(log.newData) : null;
        
        // 处理格式化显示数据
        formattedData.value = processDataForDisplay(oldData, newData);
        
        selectedLog.value = {
            oldData: oldData ? JSON.stringify(oldData, null, 2) : null,
            newData: newData ? JSON.stringify(newData, null, 2) : null,
        };
    } catch (error) {
        console.error('解析日志数据失败:', error);
        formattedData.value = [];
        selectedLog.value = {
            oldData: log.oldData,
            newData: log.newData,
        };
    }
    
    dialogVisible.value = true;
};

// 复制所有数据
const copyAllData = async () => {
    let content = '';
    if (selectedLog.value?.oldData) {
        content += '修改前数据：\n' + selectedLog.value.oldData + '\n\n';
    }
    if (selectedLog.value?.newData) {
        content += '修改后数据：\n' + selectedLog.value.newData;
    }
    
    try {
        await navigator.clipboard.writeText(content);
        const { MessagePlugin } = await import('tdesign-vue-next');
        MessagePlugin.success('数据已复制到剪贴板');
    } catch (error) {
        const { MessagePlugin } = await import('tdesign-vue-next');
        MessagePlugin.error('复制失败，请手动选择复制');
    }
};

// 页面加载时获取日志列表
onMounted(() => {
    fetchLogs();
});
</script>

<style scoped>
.log-container {
    padding: 20px;
}

.data-detail-container {
    max-height: 70vh;
    overflow-y: auto;
}

.data-section {
    margin-bottom: 24px;
}

.data-section:last-child {
    margin-bottom: 0;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 12px;
    background-color: var(--td-bg-color-container-hover);
    border-radius: 6px;
    font-weight: 600;
    font-size: 14px;
    color: var(--td-text-color-primary);
}

.section-icon {
    margin-right: 8px;
    color: var(--td-brand-color);
}

/* 详情表格样式 */
.field-label {
    font-weight: 500;
    color: var(--td-text-color-primary);
}

.old-value {
    color: var(--td-text-color-secondary);
}

.new-value {
    color: var(--td-text-color-primary);
}

.new-value.changed {
    color: var(--td-warning-color);
    font-weight: 500;
    background-color: var(--td-warning-color-1);
    padding: 2px 6px;
    border-radius: 3px;
}

.unchanged {
    color: var(--td-text-color-placeholder);
    font-size: 12px;
}

/* 原始数据区域 */
.raw-data-section {
    margin-top: 16px;
    border-top: 1px solid var(--td-component-border);
    padding-top: 16px;
}

.json-container {
    position: relative;
    border: 1px solid var(--td-component-border);
    border-radius: 6px;
    background-color: var(--td-bg-color-code);
    overflow: hidden;
}

.json-section {
    margin-bottom: 16px;
}

.json-section:last-child {
    margin-bottom: 0;
}

.json-section strong {
    display: block;
    margin-bottom: 8px;
    color: var(--td-text-color-primary);
    font-size: 14px;
}

.json-content {
    margin: 0;
    padding: 16px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    color: var(--td-text-color-primary);
    background: transparent;
    overflow-x: auto;
    white-space: pre;
    word-wrap: normal;
}

.json-content::-webkit-scrollbar {
    height: 6px;
}

.json-content::-webkit-scrollbar-track {
    background: var(--td-bg-color-container);
    border-radius: 3px;
}

.json-content::-webkit-scrollbar-thumb {
    background: var(--td-bg-color-component-hover);
    border-radius: 3px;
}

.json-content::-webkit-scrollbar-thumb:hover {
    background: var(--td-bg-color-component-pressed);
}

.empty-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: var(--td-text-color-placeholder);
    text-align: center;
}

.empty-data p {
    margin: 16px 0 0 0;
    font-size: 14px;
}

/* JSON 语法高亮样式 */
.json-content {
    color: var(--td-text-color-primary);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .json-container {
        background-color: #1a1a1a;
        border-color: #333;
    }
    
    .json-content {
        color: #e6e6e6;
    }
    
    .section-header {
        background-color: #2a2a2a;
        color: #e6e6e6;
    }
}
</style>
