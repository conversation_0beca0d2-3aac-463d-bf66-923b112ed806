<template>
    <div class="users-container">
        <t-card title="用户管理">
            <template #actions>
                <has-permission permission="user:create">
                    <t-button theme="primary" @click="handleAddUser">
                        <template #icon>
                            <t-icon name="add" />
                        </template>
                        新增用户
                    </t-button>
                </has-permission>
            </template>

            <t-table :data="users" :columns="columns" :loading="loading" :pagination="pagination" row-key="id"
                @page-change="onPageChange">
                <template #role="{ row }">
                    <t-tag theme="primary" variant="light">
                        {{ row.role?.name || '-' }}
                    </t-tag>
                </template>

                <template #status="{ row }">
                    <t-tag :theme="row.status === 1 ? 'success' : 'danger'" variant="light">
                        {{ row.status === 1 ? '正常' : '禁用' }}
                    </t-tag>
                </template>

                <template #createTime="{ row }">
                    {{ formatDate(row.createdAt) }}
                </template>

                <template #op="slotProps">
                    <t-space>
                        <has-permission permission="user:edit">
                            <t-button theme="primary" variant="text" @click="handleEdit(slotProps.row)">
                                编辑
                            </t-button>
                        </has-permission>
                        <has-permission permission="user:delete">
                            <t-button theme="danger" variant="text" @click="handleDelete(slotProps.row)">
                                删除
                            </t-button>
                        </has-permission>
                    </t-space>
                </template>
            </t-table>
        </t-card>

        <!-- 新增/编辑用户抽屉 -->
        <ClientOnly>
            <t-drawer v-model:visible="drawerVisible" :size="drawerSize" :header="drawerTitle" :footer="true"
                @close="onDrawerClose">
                <template #body>
                    <t-form ref="form" :data="formData" :rules="rules" @submit="onSubmit" label-align="top">
                        <t-form-item label="用户名" name="username">
                            <t-input v-model="formData.username" placeholder="请输入用户名" />
                        </t-form-item>

                        <t-form-item label="手机号码" name="phone">
                            <t-input v-model="formData.phone" placeholder="请输入手机号码" />
                        </t-form-item>

                        <t-form-item label="角色" name="roleId">
                            <t-select v-model="formData.roleId" placeholder="请选择角色" :options="roleOptions" />
                        </t-form-item>

                        <t-form-item label="状态" name="status">
                            <t-radio-group v-model="formData.status" :options="statusOptions" />
                        </t-form-item>

                        <t-form-item v-if="!isEdit" label="密码" name="password">
                            <t-input v-model="formData.password" type="password" placeholder="请输入密码" />
                        </t-form-item>

                        <t-form-item v-if="!isEdit" label="确认密码" name="confirmPassword">
                            <t-input v-model="formData.confirmPassword" type="password" placeholder="请再次输入密码" />
                        </t-form-item>
                    </t-form>
                </template>

                <template #footer>
                    <t-space>
                        <t-button v-if="isEdit" theme="warning" @click="handleResetPassword">
                            重置密码
                        </t-button>
                        <t-button theme="default" variant="base" @click="onDrawerClose">取消</t-button>
                        <t-button theme="primary" @click="onSubmit" :loading="submitting">确定</t-button>
                    </t-space>
                </template>
            </t-drawer>
        </ClientOnly>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import type { FormRules } from 'tdesign-vue-next';

definePageMeta({
    layout: 'dashboard'
});

const users = ref([]);
const loading = ref(false);
const pagination = ref({
    total: 0,
    pageSize: 10,
    current: 1,
});

// 抽屉相关
const drawerVisible = ref(false);
const drawerSize = '500px';
const drawerTitle = ref('新增用户');
const submitting = ref(false);
const isEdit = ref(false);

interface UserFormData {
    id: number | null;
    username: string;
    phone: string;
    roleId: number | undefined;
    status: number;
    password: string;
    confirmPassword: string;
}

// 表单数据
const formData = ref<UserFormData>({
    id: null,
    username: '',
    phone: '',
    roleId: undefined,
    status: 1,
    password: '',
    confirmPassword: ''
});

// 角色选项
const roleOptions = ref([]);

// 状态选项
const statusOptions = [
    { label: '正常', value: 1 },
    { label: '禁用', value: 2 }
];

// 表单规则
const rules: FormRules<UserFormData> = {
    username: [{ required: true, message: '请输入用户名', type: 'error' }],
    phone: [{ required: true, message: '请输入手机号码', type: 'error' }],
    roleId: [{ required: true, message: '请选择角色', type: 'error' }],
    password: [{ required: true, message: '请输入密码', type: 'error' }],
    confirmPassword: [
        { required: true, message: '请再次输入密码', type: 'error' },
        {
            validator: (val: string) => val === formData.value.password,
            message: '两次输入的密码不一致',
            type: 'error',
            trigger: 'blur'
        }
    ]
};

// 定义表格列
const columns = [
    {
        colKey: 'username',
        title: '用户名',
        width: 120,
    },
    {
        colKey: 'phone',
        title: '手机号码',
        width: 150,
    },
    {
        colKey: 'role',
        title: '角色',
        width: 120,
        cell: 'role',
    },
    {
        colKey: 'status',
        title: '状态',
        width: 120,
        cell: 'status',
    },
    {
        colKey: 'createdAt',
        title: '创建时间',
        width: 180,
        cell: 'createTime',
    },
    {
        colKey: 'op',
        title: '操作',
        width: 160,
        cell: 'op',
    },
];

interface User {
    id: number;
    username: string;
    phone: string;
    status: number;
    role?: { id: number; name: string };
}

// 获取用户列表
const fetchUsers = async () => {
    loading.value = true;
    const { post } = useHttp();
    const response = await post('/api/sys/users/list', {
        pageSize: pagination.value.pageSize,
        current: pagination.value.current
    });
    users.value = response.list;
    pagination.value.total = response.total;
    loading.value = false;
};

// 获取角色列表
const fetchRoles = async () => {
    const { post } = useHttp();
    const response = await post('/api/sys/roles/list');
    roleOptions.value = response.map((role: { id: number; name: string }) => ({
        label: role.name,
        value: role.id
    }));
};

// 格式化日期
const formatDate = (date: Date | string | null) => {
    if (!date) return '-';
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}`
};

// 页码变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
    pagination.value.current = pageInfo.current;
    pagination.value.pageSize = pageInfo.pageSize;
    fetchUsers();
};

// 添加用户
const handleAddUser = async () => {
    isEdit.value = false;
    drawerTitle.value = '新增用户';
    formData.value = {
        id: null,
        username: '',
        phone: '',
        roleId: undefined,
        status: 1,
        password: '',
        confirmPassword: ''
    };
    await fetchRoles();
    drawerVisible.value = true;
};

// 编辑用户
const handleEdit = async (user: User) => {
    isEdit.value = true;
    drawerTitle.value = '编辑用户';
    await fetchRoles();

    formData.value = {
        id: user.id,
        username: user.username,
        phone: user.phone,
        roleId: user.role?.id || undefined,
        status: user.status ?? 1,  // 如果status不存在则默认为1
        password: '',
        confirmPassword: ''
    };

    drawerVisible.value = true;
};

// 删除用户
const handleDelete = (user: User) => {
    const confirm = DialogPlugin.confirm({
        header: '确认删除',
        body: `确定要删除用户"${user.username}"吗？删除后数据不可恢复！`,
        theme: 'danger',
        confirmBtn: {
            content: '确认删除',
            theme: 'danger',
        },
        cancelBtn: {
            content: '取消',
            theme: 'default',
        },
        onConfirm: async () => {
            try {
                const { post } = useHttp();
                await post('/api/sys/users/delete', { id: user.id });

                MessagePlugin.success('删除成功');
                fetchUsers();
            } finally {
                confirm.hide();
            }

        }
    });
};

// 关闭抽屉
const onDrawerClose = () => {
    drawerVisible.value = false;
    formData.value = {
        id: null,
        username: '',
        phone: '',
        roleId: undefined,
        status: 1,
        password: '',
        confirmPassword: ''
    };
    isEdit.value = false;
};

// 提交表单
const onSubmit = async () => {
    // 验证表单
    if (!formData.value.username || !formData.value.phone || !formData.value.roleId) {
        MessagePlugin.error('请填写完整信息');
        return;
    }

    if (!isEdit.value && (!formData.value.password || !formData.value.confirmPassword)) {
        MessagePlugin.error('请填写密码信息');
        return;
    }

    if (!isEdit.value && formData.value.password !== formData.value.confirmPassword) {
        MessagePlugin.error('两次输入的密码不一致');
        return;
    }

    try {
        submitting.value = true;
        const { post } = useHttp();
        const url = isEdit.value ? '/api/sys/users/update' : '/api/sys/users/save';
        await post(url, formData.value);
        MessagePlugin.success(isEdit.value ? '更新成功' : '创建成功');
        drawerVisible.value = false;
        fetchUsers();
    } finally {
        submitting.value = false;
    }
};

// 重置密码
const handleResetPassword = async () => {
    const confirm = DialogPlugin.confirm({
        header: '确认重置密码',
        body: `确定要重置用户"${formData.value.username}"的密码吗？`,
        theme: 'warning',
        confirmBtn: {
            content: '确认重置',
            theme: 'warning',
        },
        cancelBtn: {
            content: '取消',
            theme: 'default',
        },
        onConfirm: async () => {
            try {
                const newPassword = generateRandomPassword();
                const { post } = useHttp();
                await post('/api/sys/users/reset-password', {
                    id: formData.value.id,
                    password: newPassword
                });

                MessagePlugin.success('密码重置成功');

                // 显示新密码
                DialogPlugin.alert({
                    header: '重置密码成功',
                    body: `账号：${formData.value.username} 新密码 ${newPassword} 请妥善保管此密码，关闭窗口后将无法再次查看`,
                    confirmBtn: {
                        content: '复制并关闭',
                        theme: 'primary',
                        variant: 'base',
                    },
                    onConfirm: () => {
                        // 复制密码到剪贴板
                        navigator.clipboard.writeText(newPassword)
                            .then(() => {
                                MessagePlugin.success('密码已复制到剪贴板');
                            })
                            .catch(() => {
                                MessagePlugin.warning('密码复制失败，请手动复制');
                            });
                    }
                });
            } catch (error) {
                console.error('重置密码失败:', error);
                MessagePlugin.error('重置密码失败，请重试');
            } finally {
                confirm.hide();
            }
        }
    });
};

// 生成6位随机数字密码
const generateRandomPassword = () => {
    const digits = '0123456789';
    let password = '';
    for (let i = 0; i < 6; i++) {
        password += digits[Math.floor(Math.random() * digits.length)];
    }
    return password;
};

// 页面加载时获取用户列表
onMounted(() => {
    fetchUsers();
});
</script>

<style scoped>
.users-container {
    padding: 20px;
}
</style>
