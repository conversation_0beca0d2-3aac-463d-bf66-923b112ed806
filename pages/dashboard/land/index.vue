<template>
    <div class="lands-container">
        <t-card title="地块管理">
            <template #actions>
                <!-- <has-permission permission="land:create"> -->
                    <t-button theme="primary" @click="handleAddLand">
                        <template #icon>
                            <t-icon name="add" />
                        </template>
                        新增地块
                    </t-button>
                <!-- </has-permission> -->
            </template>

            <t-table :data="lands" :columns="columns" :loading="loading" :pagination="pagination" row-key="id"
                @page-change="onPageChange">
                <template #area="{ row }">
                    {{ row.area }} m²
                </template>

                <template #status="{ row }">
                    <t-tag :theme="row.status === 1 ? 'success' : 'warning'" variant="light">
                        {{ row.status === 1 ? '已租' : '未租' }}
                    </t-tag>
                </template>
                <template #op="slotProps">
                    <t-space>
                        <!-- <has-permission permission="land:edit"> -->
                            <t-button theme="primary" variant="text" @click="handleEdit(slotProps.row)">
                                编辑
                            </t-button>
                        <!-- </has-permission> -->
                        <!-- <has-permission permission="land:delete"> -->
                            <t-button theme="danger" variant="text" @click="handleDelete(slotProps.row)">
                                删除
                            </t-button>
                        <!-- </has-permission> -->
                    </t-space>
                </template>
            </t-table>
        </t-card>

        <!-- 新增/编辑地块抽屉 -->
        <ClientOnly>
            <t-drawer v-model:visible="drawerVisible" :size="drawerSize" :header="drawerTitle" :footer="true"
                @close="onDrawerClose">
                <template #body>
                    <t-form ref="formRef" :data="formData" :rules="rules" @submit="onSubmit" label-align="top">
                        <t-form-item label="地块名称" name="name">
                            <t-input v-model="formData.name" placeholder="请输入地块名称" />
                        </t-form-item>

                        <t-form-item label="地块面积(平方米)" name="area">
                            <t-input v-model="formData.area" placeholder="请输入地块面积"/>
                        </t-form-item>

                        <t-form-item label="租赁状态" name="status">
                            <t-radio-group v-model="formData.status" :options="statusOptions" />
                        </t-form-item>
                    </t-form>
                </template>

                <template #footer>
                    <t-space>
                        <t-button theme="default" variant="base" @click="onDrawerClose">取消</t-button>
                        <t-button theme="primary" @click="handleSubmit" :loading="submitting">确定</t-button>
                    </t-space>
                </template>
            </t-drawer>
        </ClientOnly>
    </div>
</template>

<script setup lang="tsx">
import { ref, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import type { FormRules } from 'tdesign-vue-next';

definePageMeta({
    layout: 'dashboard'
});

const lands = ref([]);
const loading = ref(false);
const pagination = ref({
    total: 0,
    pageSize: 10,
    current: 1,
});

// 抽屉相关
const drawerVisible = ref(false);
const drawerSize = '500px';
const drawerTitle = ref('新增地块');
const submitting = ref(false);
const isEdit = ref(false);
const formRef = ref();

interface LandFormData {
    id: string | null;
    name: string;
    area: string;
    status: number;
}

// 表单数据
const formData = ref<LandFormData>({
    id: null,
    name: '',
    area: '',
    status: 2
});

// 状态选项
const statusOptions = [
    { label: '已租', value: 1 },
    { label: '未租', value: 2 }
];

// 表单规则
const rules: FormRules<LandFormData> = {
    name: [
        { required: true, message: '请输入地块名称', type: 'error' },
        { min: 1, max: 50, message: '地块名称长度在1-50个字符', type: 'error' }
    ],
    area: [
        { required: true, message: '请输入地块面积', type: 'error' },
        { 
            pattern: /^\d+(\.\d+)?$/, 
            message: '请输入正确的面积数值', 
            type: 'error' 
        },
        {
            validator: (val: string) => {
                const num = parseFloat(val);
                return num > 0 && num <= 999999;
            },
            message: '地块面积必须大于0且不超过999999平方米',
            type: 'error'
        }
    ]
};

// 定义表格列
const columns = [
    {
        colKey: 'name',
        title: '地块名称',
        width: 160,
    },
    {
        colKey: 'area',
        title: '地块面积',
        width: 120,
        cell: 'area',
    },
    {
        colKey: 'status',
        title: '租赁状态',
        width: 120,
        cell: 'status',
    },
    {
        colKey: 'op',
        title: '操作',
        width: 160,
        cell: 'op',
    },
];

interface Land {
    id: string;
    name: string;
    area: string;
    status: number;
    creator?: { id: number; username: string };
    createdAt: Date | string;
}

// 获取地块列表
const fetchLands = async () => {
    loading.value = true;
    try {
        const { post } = useHttp();
        const response = await post('/api/lands/list', {
            pageSize: pagination.value.pageSize,
            current: pagination.value.current
        });
        
        lands.value = response.list;
        pagination.value.total = response.total;
    } catch (error) {
        MessagePlugin.error('获取地块列表失败');
    } finally {
        loading.value = false;
    }
};

// 页码变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
    pagination.value.current = pageInfo.current;
    pagination.value.pageSize = pageInfo.pageSize;
    fetchLands();
};

// 添加地块
const handleAddLand = () => {
    isEdit.value = false;
    drawerTitle.value = '新增地块';
    formData.value = {
        id: null,
        name: '',
        area: '',
        status: 2
    };
    drawerVisible.value = true;
};

// 编辑地块
const handleEdit = (land: Land) => {
    isEdit.value = true;
    drawerTitle.value = '编辑地块';

    formData.value = {
        id: land.id,
        name: land.name,
        area: land.area,
        status: land.status
    };

    drawerVisible.value = true;
};

// 删除地块
const handleDelete = (land: Land) => {
    const confirm = DialogPlugin.confirm({
        header: '确认删除',
        body: `确定要删除地块"${land.name}"吗？删除后数据不可恢复！`,
        theme: 'danger',
        confirmBtn: {
            content: '确认删除',
            theme: 'danger',
        },
        cancelBtn: {
            content: '取消',
            theme: 'default',
        },
        onConfirm: async () => {
            try {
                const { post } = useHttp();
                await post('/api/lands/delete', { id: land.id });

                MessagePlugin.success('删除成功');
                fetchLands();
            } catch (error) {
                MessagePlugin.error('删除失败');
            } finally {
                confirm.hide();
            }
        }
    });
};

// 关闭抽屉
const onDrawerClose = () => {
    drawerVisible.value = false;
    formData.value = {
        id: null,
        name: '',
        area: '',
        status: 2
    };
    isEdit.value = false;
    // 重置表单验证状态
    formRef.value?.reset();
};

// 手动触发表单验证
const handleSubmit = () => {
    formRef.value?.submit();
};

// 提交表单
const onSubmit = async ({ validateResult, firstError }: { validateResult: boolean, firstError: string }) => {
    if (validateResult !== true) {
        MessagePlugin.error(firstError);
        return;
    }

    try {
        submitting.value = true;
        const { post } = useHttp();
        const url = isEdit.value ? '/api/lands/update' : '/api/lands/save';
        await post(url, formData.value);
        
        MessagePlugin.success(isEdit.value ? '更新成功' : '创建成功');
        drawerVisible.value = false;
        fetchLands();
    } catch (error) {
        MessagePlugin.error(isEdit.value ? '更新失败' : '创建失败');
    } finally {
        submitting.value = false;
    }
};

// 页面加载时获取地块列表
onMounted(() => {
    fetchLands();
});
</script>

<style scoped>
.lands-container {
    padding: 20px;
}
</style>