<template>
  <div class="crops-container">
    <t-card title="作物管理">
      <template #actions>
        <t-button theme="primary" @click="handleAddCrop">
          <template #icon>
            <t-icon name="add" />
          </template>
          新增作物
        </t-button>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <t-form layout="inline" @submit="handleSearch" @reset="handleReset">
          <t-form-item label="作物名称">
            <t-input v-model="searchForm.name" placeholder="请输入作物名称" clearable style="width: 200px;" />
          </t-form-item>
          <t-form-item label="地块">
            <t-select v-model="searchForm.landId" placeholder="请选择地块" clearable style="width: 200px;">
              <t-option
                v-for="land in landOptions"
                :key="land.id"
                :value="land.id"
                :label="`${land.name} (${land.area}m²)`"
              />
            </t-select>
          </t-form-item>
                     <t-form-item label="状态">
             <t-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 150px;">
               <t-option :value="1" label="生长中" />
               <t-option :value="2" label="已成熟" />
               <t-option :value="3" label="已收获" />
             </t-select>
           </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" type="submit">
                <template #icon><t-icon name="search" /></template>
                查询
              </t-button>
              <t-button theme="default" type="reset">
                <template #icon><t-icon name="refresh" /></template>
                重置
              </t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </div>

      <t-table :data="crops" :columns="columns" :loading="loading" :pagination="pagination" row-key="id"
        @page-change="onPageChange">
        <template #plantingArea="{ row }">
          {{ row.plantingArea }} m²
        </template>

        <template #plantingDate="{ row }">
          {{ row.plantingDate ? new Date(row.plantingDate).toLocaleDateString() : '-' }}
        </template>

        <template #expectedHarvestDate="{ row }">
          {{ row.expectedHarvestDate ? new Date(row.expectedHarvestDate).toLocaleDateString() : '-' }}
        </template>

        <template #actualHarvestDate="{ row }">
          {{ row.actualHarvestDate ? new Date(row.actualHarvestDate).toLocaleDateString() : '-' }}
        </template>

        <template #status="{ row }">
          <t-tag :theme="row.status === 1 ? 'warning' : (row.status === 2 ? 'success' : 'primary')" variant="light">
            {{ row.status === 1 ? '生长中' : (row.status === 2 ? '已成熟' : '已收获') }}
          </t-tag>
        </template>

        <template #op="slotProps">
          <t-space>
            <t-button theme="primary" variant="text" @click="handleEdit(slotProps.row)">
              编辑
            </t-button>
            <t-button theme="danger" variant="text" @click="handleDelete(slotProps.row)">
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 新增/编辑作物抽屉 -->
    <ClientOnly>
      <t-drawer v-model:visible="drawerVisible" :size="drawerSize" :header="drawerTitle" :footer="true"
        @close="onDrawerClose">
        <template #body>
          <t-form ref="formRef" :data="formData" :rules="rules" @submit="onSubmit" label-align="top">
                                    <t-form-item label="作物图标" name="emoji">
                            <div class="emoji-selector">
                                <div class="emoji-grid">
                                    <div 
                                        v-for="emoji in cropEmojis" 
                                        :key="emoji.code"
                                        :class="['emoji-item', { active: formData.emoji === emoji.code }]"
                                        @click="selectEmoji(emoji.code)"
                                        :title="emoji.name"
                                    >
                                        {{ emoji.code }}
                                    </div>
                                </div>
                            </div>
                        </t-form-item>

                        <t-form-item label="作物名称" name="name">
                            <t-input v-model="formData.name" placeholder="请输入作物名称" />
                        </t-form-item>

            <t-form-item label="地块" name="landId">
              <t-select v-model="formData.landId" placeholder="请选择地块">
                <t-option
                  v-for="land in landOptions"
                  :key="land.id"
                  :value="land.id"
                  :label="`${land.name} (${land.area}m²)`"
                />
              </t-select>
            </t-form-item>

            <t-form-item label="种植面积(平方米)" name="plantingArea">
              <t-input v-model="formData.plantingArea" placeholder="请输入种植面积" />
              <div v-if="selectedLandArea" class="area-tip">
                最大可种植面积：{{ selectedLandArea }}m²
              </div>
            </t-form-item>

            <t-form-item label="种植日期" name="plantingDate">
              <t-date-picker
                v-model="formData.plantingDate"
                placeholder="请选择种植日期"
                clearable
                format="YYYY-MM-DD"
              />
            </t-form-item>

            <t-form-item label="预计收获日期" name="expectedHarvestDate">
              <t-date-picker
                v-model="formData.expectedHarvestDate"
                placeholder="请选择预计收获日期"
                clearable
                format="YYYY-MM-DD"
              />
            </t-form-item>

            <t-form-item label="实际收获日期" name="actualHarvestDate">
              <t-date-picker
                v-model="formData.actualHarvestDate"
                placeholder="请选择实际收获日期"
                clearable
                format="YYYY-MM-DD"
              />
            </t-form-item>

            <t-form-item label="作物状态" name="status">
              <t-select v-model="formData.status" placeholder="请选择状态">
                <t-option :value="1" label="生长中" />
                <t-option :value="2" label="已成熟" />
                <t-option :value="3" label="已收获" />
              </t-select>
            </t-form-item>
          </t-form>
        </template>

        <template #footer>
          <t-space>
            <t-button theme="default" variant="base" @click="onDrawerClose">取消</t-button>
            <t-button theme="primary" @click="handleSubmit" :loading="submitting">确定</t-button>
          </t-space>
        </template>
      </t-drawer>
    </ClientOnly>
  </div>
</template>

<script setup lang="tsx">
import { ref, onMounted, computed } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import type { FormRules } from 'tdesign-vue-next';

definePageMeta({
  layout: 'dashboard'
});

const crops = ref([]);
const landOptions = ref<Array<{id: string, name: string, area: string}>>([]);
const loading = ref(false);
const pagination = ref({
  total: 0,
  pageSize: 10,
  current: 1,
});

// 搜索表单
const searchForm = ref({
  name: '',
  landId: '',
  status: '',
});

// 抽屉相关
const drawerVisible = ref(false);
const drawerSize = '600px';
const drawerTitle = ref('新增作物');
const submitting = ref(false);
const isEdit = ref(false);
const formRef = ref();

interface CropFormData {
    id: string | null;
    emoji: string;
    name: string;
    landId: string;
    plantingArea: string;
    plantingDate: string;
    expectedHarvestDate: string;
    actualHarvestDate: string;
    status: number;
}

// 表单数据
const formData = ref<CropFormData>({
    id: null,
    emoji: '🌱',
    name: '',
    landId: '',
    plantingArea: '',
    plantingDate: '',
    expectedHarvestDate: '',
    actualHarvestDate: '',
    status: 1,
});

// 作物emoji列表
const cropEmojis = [
    // 蔬菜类
    { code: '🥬', name: '白菜' },
    { code: '🥒', name: '黄瓜' },
    { code: '🍅', name: '番茄' },
    { code: '🥕', name: '胡萝卜' },
    { code: '🌶️', name: '辣椒' },
    { code: '🫑', name: '青椒' },
    { code: '🥔', name: '土豆' },
    { code: '🧅', name: '洋葱' },
    { code: '🧄', name: '大蒜' },
    { code: '🥦', name: '西兰花' },
    { code: '🍆', name: '茄子' },
    { code: '🌽', name: '玉米' },
    { code: '🫛', name: '豌豆' },
    { code: '🍄', name: '蘑菇' },
    
    // 水果类
    { code: '🍎', name: '苹果' },
    { code: '🍊', name: '橙子' },
    { code: '🍌', name: '香蕉' },
    { code: '🍇', name: '葡萄' },
    { code: '🍓', name: '草莓' },
    { code: '🫐', name: '蓝莓' },
    { code: '🍑', name: '桃子' },
    { code: '🍒', name: '樱桃' },
    { code: '🥝', name: '猕猴桃' },
    { code: '🍍', name: '菠萝' },
    { code: '🥭', name: '芒果' },
    { code: '🍉', name: '西瓜' },
    { code: '🍈', name: '哈密瓜' },
    { code: '🍋', name: '柠檬' },
    { code: '🍐', name: '梨' },
    { code: '🥥', name: '椰子' },
    
    // 粮食作物
    { code: '🌾', name: '小麦' },
    { code: '🫘', name: '豆类' },
    { code: '🌰', name: '栗子' },
    { code: '🥜', name: '花生' },
    { code: '🌻', name: '向日葵' },
    
    // 中草药和其他
    { code: '🌿', name: '香草' },
    { code: '🌱', name: '幼苗' },
    { code: '☘️', name: '三叶草' },
    { code: '🍀', name: '四叶草' },
    { code: '🌳', name: '果树' },
    { code: '🎋', name: '竹子' },
];

// 计算选中地块的面积
const selectedLandArea = computed(() => {
  const selectedLand = landOptions.value.find((land) => land.id === formData.value.landId);
  return selectedLand ? parseFloat(selectedLand.area) : null;
});

// 表单规则
const rules: FormRules<CropFormData> = {
  name: [
    { required: true, message: '请输入作物名称', type: 'error' },
    { min: 2, max: 50, message: '作物名称长度在2-50个字符', type: 'error' }
  ],
  landId: [
    { required: true, message: '请选择地块', type: 'error' }
  ],
  plantingArea: [
    { required: true, message: '请输入种植面积', type: 'error' },
    { 
      pattern: /^\d+(\.\d+)?$/, 
      message: '请输入正确的面积数值', 
      type: 'error' 
    },
    {
      validator: (val: string) => {
        const num = parseFloat(val);
        if (num <= 0) return false;
        if (selectedLandArea.value && num > selectedLandArea.value) return false;
        return true;
      },
      message: '种植面积必须大于0且不能超过地块面积',
      type: 'error'
    }
  ],
  plantingDate: [
    { required: true, message: '请选择种植日期', type: 'error' }
  ],
  expectedHarvestDate: [
    { required: true, message: '请选择预计收获日期', type: 'error' },
    {
      validator: (val: string) => {
        if (val && formData.value.plantingDate && new Date(val) <= new Date(formData.value.plantingDate)) {
          return false;
        }
        return true;
      },
      message: '预计收获日期必须晚于种植日期',
      type: 'error'
    }
  ],
  actualHarvestDate: [
    {
      validator: (val: string) => {
        if (val && formData.value.plantingDate && new Date(val) <= new Date(formData.value.plantingDate)) {
          return false;
        }
        return true;
      },
      message: '实际收获日期必须晚于种植日期',
      type: 'error'
    }
  ],
  status: [
    { required: true, message: '请选择作物状态', type: 'error' }
  ]
};

// 定义表格列
const columns = [
      {
        colKey: 'name',
        title: '作物名称',
        width: 150,
        cell: (h: any, { row }: any) => (
            <div class="crop-name-cell">
                <span class="crop-emoji">{row.emoji || '🌱'}</span>
                <span class="crop-name">{row.name}</span>
            </div>
        ),
    },
  {
    colKey: 'landName',
    title: '地块',
    width: 120,
  },
  {
    colKey: 'plantingArea',
    title: '种植面积',
    width: 120,
    cell: 'plantingArea',
  },
  {
    colKey: 'plantingDate',
    title: '种植日期',
    width: 120,
    cell: 'plantingDate',
  },
  {
    colKey: 'expectedHarvestDate',
    title: '预计收获日期',
    width: 140,
    cell: 'expectedHarvestDate',
  },
  {
    colKey: 'actualHarvestDate',
    title: '实际收获日期',
    width: 140,
    cell: 'actualHarvestDate',
  },
  {
    colKey: 'status',
    title: '状态',
    width: 100,
    cell: 'status',
  },
  {
    colKey: 'op',
    title: '操作',
    width: 160,
    cell: 'op',
  },
];

interface Crop {
    id: string;
    emoji: string;
    name: string;
    landId: string;
    landName: string;
    plantingArea: string;
    plantingDate: string;
    expectedHarvestDate: string;
    actualHarvestDate: string;
    status: number;
    createdAt: string;
}

// 获取作物列表
const fetchCrops = async () => {
  loading.value = true;
  try {
    const { post } = useHttp();
    const response = await post('/api/crops/list', {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      name: searchForm.value.name,
      landId: searchForm.value.landId,
      status: searchForm.value.status,
    });
    
    crops.value = response.list;
    pagination.value.total = response.total;
  } catch (error) {
    MessagePlugin.error('获取作物列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取地块选项
const fetchLandOptions = async () => {
  try {
    const { get } = useHttp();
    const response = await get('/api/crops/available-lands');
    landOptions.value = response;
  } catch (error) {
    MessagePlugin.error('获取地块列表失败');
  }
};

// 页码变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchCrops();
};

// 搜索
const handleSearch = () => {
  pagination.value.current = 1;
  fetchCrops();
};

// 重置搜索
const handleReset = () => {
  searchForm.value.name = '';
  searchForm.value.landId = '';
  searchForm.value.status = '';
  pagination.value.current = 1;
  fetchCrops();
};

// 添加作物
const handleAddCrop = () => {
  isEdit.value = false;
  drawerTitle.value = '新增作物';
  formData.value = {
    id: null,
    emoji: '🌱',
    name: '',
    landId: '',
    plantingArea: '',
    plantingDate: '',
    expectedHarvestDate: '',
    actualHarvestDate: '',
    status: 1,
  };
  drawerVisible.value = true;
};

// 编辑作物
const handleEdit = (crop: Crop) => {
  isEdit.value = true;
  drawerTitle.value = '编辑作物';

      formData.value = {
        id: crop.id,
        emoji: crop.emoji || '🌱',
        name: crop.name,
        landId: crop.landId,
        plantingArea: crop.plantingArea,
        plantingDate: crop.plantingDate ? new Date(crop.plantingDate).toISOString().split('T')[0] : '',
        expectedHarvestDate: crop.expectedHarvestDate ? new Date(crop.expectedHarvestDate).toISOString().split('T')[0] : '',
        actualHarvestDate: crop.actualHarvestDate ? new Date(crop.actualHarvestDate).toISOString().split('T')[0] : '',
        status: crop.status || 1,
    };

  drawerVisible.value = true;
};

// 删除作物
const handleDelete = (crop: Crop) => {
  const confirm = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除作物"${crop.name}"吗？删除后数据不可恢复！`,
    theme: 'danger',
    confirmBtn: {
      content: '确认删除',
      theme: 'danger',
    },
    cancelBtn: {
      content: '取消',
      theme: 'default',
    },
    onConfirm: async () => {
      try {
        const { post } = useHttp();
        await post('/api/crops/delete', { id: crop.id });

        MessagePlugin.success('删除成功');
        fetchCrops();
      } catch (error) {
        MessagePlugin.error('删除失败');
      } finally {
        confirm.hide();
      }
    }
  });
};

// 关闭抽屉
const onDrawerClose = () => {
  drawerVisible.value = false;
  formData.value = {
    id: null,
    emoji: '🌱',
    name: '',
    landId: '',
    plantingArea: '',
    plantingDate: '',
    expectedHarvestDate: '',
    actualHarvestDate: '',
    status: 1,
  };
  isEdit.value = false;
  // 重置表单验证状态
  formRef.value?.reset();
};

// 手动触发表单验证
const handleSubmit = () => {
  formRef.value?.submit();
};

// 提交表单
const onSubmit = async (context: any) => {
  const { validateResult, firstError } = context;
  if (validateResult !== true) {
    MessagePlugin.error(firstError);
    return;
  }

  try {
    submitting.value = true;
    const { post } = useHttp();
    const url = isEdit.value ? '/api/crops/update' : '/api/crops/save';
    await post(url, formData.value);
    
    MessagePlugin.success(isEdit.value ? '更新成功' : '创建成功');
    drawerVisible.value = false;
    fetchCrops();
  } catch (error) {
    MessagePlugin.error(isEdit.value ? '更新失败' : '创建失败');
  } finally {
    submitting.value = false;
  }
};

// 选择emoji
const selectEmoji = (emojiCode: string) => {
    formData.value.emoji = emojiCode;
};

// 页面加载时获取数据
onMounted(() => {
    fetchLandOptions();
    fetchCrops();
});
</script>

<style scoped>
.crops-container {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.area-tip {
    margin-top: 4px;
    font-size: 12px;
    color: #666;
}

.emoji-selector {
    margin-bottom: 16px;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
    padding: 8px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background-color: #f9fafb;
}

.emoji-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    font-size: 20px;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
    background-color: white;
    border: 2px solid transparent;
}

.emoji-item:hover {
    background-color: #f3f4f6;
    transform: scale(1.1);
}

.emoji-item.active {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.crop-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}

.crop-emoji {
    font-size: 16px;
}

.crop-name {
    font-size: 14px;
}
</style>