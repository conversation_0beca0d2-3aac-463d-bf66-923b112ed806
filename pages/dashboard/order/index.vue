<template>
    <div class="orders-container">
        <t-card title="订单管理">
            <!-- 筛选条件 -->
            <template #actions>
                <t-space>
                    <t-button theme="primary" @click="resetFilters">重置筛选</t-button>
                    <t-button theme="default" @click="fetchOrders">刷新</t-button>
                </t-space>
            </template>

            <!-- 筛选表单 -->
            <div class="filter-form">
                <t-form :data="filterForm" label-align="left" :colon="true" layout="inline">
                    <t-form-item label="订单编号">
                        <t-input v-model="filterForm.keyword" placeholder="请输入订单编号" style="width: 200px;" />
                    </t-form-item>
                    
                    <t-form-item label="租户">
                        <t-select v-model="filterForm.tenantId" placeholder="请选择租户" :options="tenantOptions" 
                            style="width: 200px;" clearable />
                    </t-form-item>
                    
                    <t-form-item label="订单状态">
                        <t-select v-model="filterForm.orderStatus" placeholder="请选择状态" :options="statusOptions" 
                            style="width: 150px;" clearable />
                    </t-form-item>
                    
                    <t-form-item label="下单日期">
                        <t-date-range-picker v-model="filterForm.dateRange" placeholder="请选择日期范围" 
                            style="width: 300px;" clearable />
                    </t-form-item>
                    
                    <t-form-item>
                        <t-button theme="primary" @click="handleSearch">搜索</t-button>
                    </t-form-item>
                </t-form>
            </div>

            <t-table :data="orders" :columns="columns" :loading="loading" :pagination="pagination" row-key="id"
                @page-change="onPageChange">
                <template #orderStatus="{ row }">
                    <t-tag :theme="getStatusTheme(row.orderStatus)" variant="light">
                        {{ getStatusText(row.orderStatus) }}
                    </t-tag>
                </template>

                <template #orderDate="{ row }">
                    {{ formatDate(row.orderDate) }}
                </template>

                <template #logisticsInfo="{ row }">
                    <div v-if="row.logisticsCompany && row.logisticsNumber">
                        <div>{{ row.logisticsCompany }}</div>
                        <div class="text-gray-500">{{ row.logisticsNumber }}</div>
                    </div>
                    <span v-else class="text-gray-400">-</span>
                </template>

                <template #op="slotProps">
                    <t-space>
                        <t-button theme="primary" variant="text" @click="handleViewDetail(slotProps.row)">
                            查看详情
                        </t-button>
                        <t-button v-if="slotProps.row.orderStatus === 1" theme="warning" variant="text" 
                            @click="handleShip(slotProps.row)">
                            发货
                        </t-button>
                    </t-space>
                </template>
            </t-table>
        </t-card>

        <!-- 订单详情弹窗 -->
        <ClientOnly>
            <t-dialog v-model:visible="detailVisible" header="订单详情" width="700px" :footer="false">
                <div v-if="orderDetail" class="detail-content">
                    <t-descriptions :column="2" bordered>
                        <t-descriptions-item label="订单编号">{{ orderDetail.orderNumber }}</t-descriptions-item>
                        <t-descriptions-item label="订单状态">
                            <t-tag :theme="getStatusTheme(orderDetail.orderStatus)" variant="light">
                                {{ getStatusText(orderDetail.orderStatus) }}
                            </t-tag>
                        </t-descriptions-item>
                        <t-descriptions-item label="下单日期">{{ formatDate(orderDetail.orderDate) }}</t-descriptions-item>
                        <t-descriptions-item label="租户信息">
                            {{ orderDetail.tenantName }}({{ orderDetail.tenantPhone }})
                        </t-descriptions-item>
                        <t-descriptions-item label="作物信息">
                            {{ orderDetail.cropName }}({{ orderDetail.cropPlantingArea }}m²)
                        </t-descriptions-item>
                        <t-descriptions-item label="地块信息">
                            {{ orderDetail.landName }}({{ orderDetail.landArea }}m²)
                        </t-descriptions-item>
                        <t-descriptions-item label="收货人">{{ orderDetail.receiverName }}</t-descriptions-item>
                        <t-descriptions-item label="收货电话">{{ orderDetail.receiverPhone }}</t-descriptions-item>
                        <t-descriptions-item label="收货地址" :span="2">{{ orderDetail.receiverAddress }}</t-descriptions-item>
                        <t-descriptions-item label="物流公司">
                            {{ orderDetail.logisticsCompany || '-' }}
                        </t-descriptions-item>
                        <t-descriptions-item label="快递单号">
                            {{ orderDetail.logisticsNumber || '-' }}
                        </t-descriptions-item>
                    </t-descriptions>
                </div>
            </t-dialog>
        </ClientOnly>

        <!-- 发货弹窗 -->
        <ClientOnly>
            <t-dialog v-model:visible="shipVisible" header="订单发货" width="500px" :confirm-btn="null" :cancel-btn="null">
                <t-form ref="shipFormRef" :data="shipForm" :rules="shipRules" @submit="onShipSubmit" label-align="top">
                    <t-form-item label="订单编号">
                        <t-input :value="currentOrder?.orderNumber" disabled />
                    </t-form-item>
                    
                    <t-form-item label="物流公司" name="logisticsCompany">
                        <t-input v-model="shipForm.logisticsCompany" placeholder="请输入物流公司名称" />
                    </t-form-item>
                    
                    <t-form-item label="快递单号" name="logisticsNumber">
                        <t-input v-model="shipForm.logisticsNumber" placeholder="请输入快递单号" />
                    </t-form-item>
                </t-form>
                
                <template #footer>
                    <t-space>
                        <t-button theme="default" @click="shipVisible = false">取消</t-button>
                        <t-button theme="primary" @click="handleShipSubmit" :loading="shipSubmitting">确认发货</t-button>
                    </t-space>
                </template>
            </t-dialog>
        </ClientOnly>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import type { FormRules } from 'tdesign-vue-next';

definePageMeta({
    layout: 'dashboard'
});

const orders = ref([]);
const loading = ref(false);
const pagination = ref({
    total: 0,
    pageSize: 10,
    current: 1,
});

// 筛选表单
const filterForm = ref({
    keyword: '',
    tenantId: '',
    orderStatus: '',
    dateRange: []
});

// 选项数据
const tenantOptions = ref([]);
const statusOptions = [
    { label: '待处理', value: 1 },
    { label: '已发货', value: 2 },
    { label: '已收货', value: 3 },
    { label: '已取消', value: 4 }
];

// 订单详情
const detailVisible = ref(false);
const orderDetail = ref<any>(null);

// 发货相关
const shipVisible = ref(false);
const currentOrder = ref<any>(null);
const shipSubmitting = ref(false);
const shipFormRef = ref();

interface ShipFormData {
    logisticsCompany: string;
    logisticsNumber: string;
}

const shipForm = ref<ShipFormData>({
    logisticsCompany: '',
    logisticsNumber: ''
});

// 发货表单规则
const shipRules: FormRules<ShipFormData> = {
    logisticsCompany: [
        { required: true, message: '请输入物流公司名称', type: 'error' },
        { min: 1, max: 50, message: '物流公司名称长度在1-50个字符', type: 'error' }
    ],
    logisticsNumber: [
        { required: true, message: '请输入快递单号', type: 'error' },
        { min: 1, max: 50, message: '快递单号长度在1-50个字符', type: 'error' }
    ]
};

// 定义表格列
const columns = [
    {
        colKey: 'orderNumber',
        title: '订单编号',
        width: 200,
    },
    {
        colKey: 'orderDate',
        title: '下单日期',
        width: 120,
        cell: 'orderDate',
    },
    {
        colKey: 'tenantName',
        title: '租户',
        width: 100,
    },
    {
        colKey: 'cropName',
        title: '作物',
        width: 100,
    },
    {
        colKey: 'landName',
        title: '地块',
        width: 100,
    },
    {
        colKey: 'receiverName',
        title: '收货人',
        width: 100,
    },
    {
        colKey: 'logisticsInfo',
        title: '物流信息',
        width: 150,
        cell: 'logisticsInfo',
    },
    {
        colKey: 'orderStatus',
        title: '状态',
        width: 100,
        cell: 'orderStatus',
    },
    {
        colKey: 'op',
        title: '操作',
        width: 160,
        cell: 'op',
    },
];

// 获取订单列表
const fetchOrders = async () => {
    loading.value = true;
    try {
        const { post } = useHttp();
        const params: any = {
            pageSize: pagination.value.pageSize,
            current: pagination.value.current,
            keyword: filterForm.value.keyword,
            tenantId: filterForm.value.tenantId,
            orderStatus: filterForm.value.orderStatus
        };

        // 处理日期范围
        if (filterForm.value.dateRange && filterForm.value.dateRange.length === 2) {
            params.startDate = filterForm.value.dateRange[0];
            params.endDate = filterForm.value.dateRange[1];
        }

        const response = await post('/api/orders/list', params);
        
        orders.value = response.list;
        pagination.value.total = response.total;
    } catch (error) {
        MessagePlugin.error('获取订单列表失败');
    } finally {
        loading.value = false;
    }
};

// 获取租户选项
const fetchTenants = async () => {
    try {
        const { get } = useHttp();
        const response = await get('/api/orders/tenants');
        tenantOptions.value = response.map((tenant: any) => ({
            label: `${tenant.name}(${tenant.phone})`,
            value: tenant.id
        }));
    } catch (error) {
        MessagePlugin.error('获取租户列表失败');
    }
};

// 获取状态主题
const getStatusTheme = (status: number) => {
    const themes: Record<number, 'warning' | 'primary' | 'success' | 'danger' | 'default'> = {
        1: 'warning',  // 待处理
        2: 'primary',  // 已发货
        3: 'success',  // 已收货
        4: 'danger',   // 已取消
        5: 'danger'    // 已取消
    };
    return themes[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: number) => {
    const texts: Record<number, string> = {
        1: '待处理',
        2: '已发货',
        3: '已收货',
        4: '已取消',
        5: '已取消'
    };
    return texts[status] || '未知';
};

// 格式化日期
const formatDate = (date: string | Date) => {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleDateString('zh-CN');
};

// 搜索
const handleSearch = () => {
    pagination.value.current = 1;
    fetchOrders();
};

// 重置筛选
const resetFilters = () => {
    filterForm.value = {
        keyword: '',
        tenantId: '',
        orderStatus: '',
        dateRange: []
    };
    pagination.value.current = 1;
    fetchOrders();
};

// 页码变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
    pagination.value.current = pageInfo.current;
    pagination.value.pageSize = pageInfo.pageSize;
    fetchOrders();
};

// 查看详情
const handleViewDetail = async (order: any) => {
    try {
        const { post } = useHttp();
        const response = await post('/api/orders/detail', { id: order.id });
        orderDetail.value = response;
        detailVisible.value = true;
    } catch (error) {
        MessagePlugin.error('获取订单详情失败');
    }
};

// 发货
const handleShip = (order: any) => {
    currentOrder.value = order;
    shipForm.value = {
        logisticsCompany: '',
        logisticsNumber: ''
    };
    shipVisible.value = true;
};

// 手动触发发货表单验证
const handleShipSubmit = () => {
    shipFormRef.value?.submit();
};

// 提交发货表单
const onShipSubmit = async (context: any) => {
    const { validateResult, firstError } = context;
    if (validateResult !== true) {
        MessagePlugin.error(firstError);
        return;
    }

    try {
        shipSubmitting.value = true;
        const { post } = useHttp();
        
        // 发货
        await post('/api/orders/ship', {
            id: currentOrder.value.id,
            logisticsCompany: shipForm.value.logisticsCompany,
            logisticsNumber: shipForm.value.logisticsNumber
        });
        
        // 获取订单详情以获取作物ID
        const orderDetail = await post('/api/orders/detail', { id: currentOrder.value.id });
        
        // 更新对应作物状态为已收获(3)
        if (orderDetail.cropId) {
            await post('/api/crops/update-status', {
                id: orderDetail.cropId,
                status: 3
            });
        }
        
        MessagePlugin.success('发货成功');
        shipVisible.value = false;
        fetchOrders();
    } catch (error) {
        MessagePlugin.error('发货失败');
    } finally {
        shipSubmitting.value = false;
    }
};

// 页面加载时获取数据
onMounted(() => {
    fetchOrders();
    fetchTenants();
});
</script>

<style scoped>
.orders-container {
    padding: 20px;
}

.filter-form {
    margin-bottom: 20px;
    padding: 16px;
    background: #fafafa;
    border-radius: 4px;
}

.detail-content {
    padding: 16px 0;
}

.text-gray-400 {
    color: #9ca3af;
}

.text-gray-500 {
    color: #6b7280;
    font-size: 12px;
}
</style>