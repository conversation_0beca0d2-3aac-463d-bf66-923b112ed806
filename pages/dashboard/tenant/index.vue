<template>
    <div class="tenants-container">
        <t-card title="租户管理">
            <template #actions>
                <t-button theme="primary" @click="handleAddTenant">
                    <template #icon>
                        <t-icon name="add" />
                    </template>
                    新增租户
                </t-button>
            </template>

            <t-table :data="tenants" :columns="columns" :loading="loading" :pagination="pagination" row-key="id"
                @page-change="onPageChange">
                <template #status="{ row }">
                    <t-tag :theme="row.status === 1 ? 'success' : 'danger'" variant="light">
                        {{ row.status === 1 ? '正常' : '禁用' }}
                    </t-tag>
                </template>

                <template #idCard="{ row }">
                    {{ maskIdCard(row.idCard) }}
                </template>

                <template #op="slotProps">
                    <t-space>
                        <t-button theme="primary" variant="text" @click="handleEdit(slotProps.row)">
                            编辑
                        </t-button>
                        <t-button theme="danger" variant="text" @click="handleDelete(slotProps.row)">
                            删除
                        </t-button>
                    </t-space>
                </template>
            </t-table>
        </t-card>

        <!-- 新增/编辑租户抽屉 -->
        <ClientOnly>
            <t-drawer v-model:visible="drawerVisible" :size="drawerSize" :header="drawerTitle" :footer="true"
                @close="onDrawerClose">
                <template #body>
                    <t-form ref="formRef" :data="formData" :rules="rules" @submit="onSubmit" label-align="top">
                        <t-form-item label="租户姓名" name="name">
                            <t-input v-model="formData.name" placeholder="请输入租户姓名" />
                        </t-form-item>

                        <t-form-item label="手机号码" name="phone">
                            <t-input v-model="formData.phone" placeholder="请输入手机号码" />
                        </t-form-item>

                        <t-form-item label="身份证号" name="idCard">
                            <t-input v-model="formData.idCard" placeholder="请输入身份证号" />
                        </t-form-item>

                        <t-form-item label="状态" name="status">
                            <t-radio-group v-model="formData.status" :options="statusOptions" />
                        </t-form-item>
                    </t-form>
                </template>

                <template #footer>
                    <t-space>
                        <t-button theme="default" variant="base" @click="onDrawerClose">取消</t-button>
                        <t-button theme="primary" @click="handleSubmit" :loading="submitting">确定</t-button>
                    </t-space>
                </template>
            </t-drawer>
        </ClientOnly>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import type { FormRules } from 'tdesign-vue-next';

definePageMeta({
    layout: 'dashboard'
});

const tenants = ref([]);
const loading = ref(false);
const pagination = ref({
    total: 0,
    pageSize: 10,
    current: 1,
});

// 抽屉相关
const drawerVisible = ref(false);
const drawerSize = '500px';
const drawerTitle = ref('新增租户');
const submitting = ref(false);
const isEdit = ref(false);
const formRef = ref();

interface TenantFormData {
    id: string | null;
    name: string;
    phone: string;
    idCard: string;
    status: number;
}

// 表单数据
const formData = ref<TenantFormData>({
    id: null,
    name: '',
    phone: '',
    idCard: '',
    status: 1
});

// 状态选项
const statusOptions = [
    { label: '正常', value: 1 },
    { label: '禁用', value: 2 }
];

// 表单规则
const rules: FormRules<TenantFormData> = {
    name: [
        { required: true, message: '请输入租户姓名', type: 'error' },
        { min: 2, max: 20, message: '租户姓名长度在2-20个字符', type: 'error' }
    ],
    phone: [
        { required: true, message: '请输入手机号码', type: 'error' },
        { 
            pattern: /^1[3-9]\d{9}$/, 
            message: '请输入正确的手机号码', 
            type: 'error' 
        }
    ],
    idCard: [
        { required: true, message: '请输入身份证号', type: 'error' },
        { 
            pattern: /^[1-9]\d{5}(18|19|20|21|22)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, 
            message: '请输入正确的身份证号', 
            type: 'error' 
        }
    ]
};

// 定义表格列
const columns = [
    {
        colKey: 'name',
        title: '租户姓名',
        width: 120,
    },
    {
        colKey: 'phone',
        title: '手机号码',
        width: 140,
    },
    {
        colKey: 'idCard',
        title: '身份证号',
        width: 180,
        cell: 'idCard',
    },
    {
        colKey: 'status',
        title: '状态',
        width: 100,
        cell: 'status',
    },
    {
        colKey: 'op',
        title: '操作',
        width: 160,
        cell: 'op',
    },
];

interface Tenant {
    id: string;
    name: string;
    phone: string;
    idCard: string;
    status: number;
    createdAt: Date | string;
}

// 获取租户列表
const fetchTenants = async () => {
    loading.value = true;
    try {
        const { post } = useHttp();
        const response = await post('/api/tenants/list', {
            pageSize: pagination.value.pageSize,
            current: pagination.value.current
        });
        
        tenants.value = response.list;
        pagination.value.total = response.total;
    } catch (error) {
        MessagePlugin.error('获取租户列表失败');
    } finally {
        loading.value = false;
    }
};

// 脱敏身份证号
const maskIdCard = (idCard: string) => {
    if (!idCard) return '-';
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
};

// 页码变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
    pagination.value.current = pageInfo.current;
    pagination.value.pageSize = pageInfo.pageSize;
    fetchTenants();
};

// 添加租户
const handleAddTenant = () => {
    isEdit.value = false;
    drawerTitle.value = '新增租户';
    formData.value = {
        id: null,
        name: '',
        phone: '',
        idCard: '',
        status: 1
    };
    drawerVisible.value = true;
};

// 编辑租户
const handleEdit = (tenant: Tenant) => {
    isEdit.value = true;
    drawerTitle.value = '编辑租户';

    formData.value = {
        id: tenant.id,
        name: tenant.name,
        phone: tenant.phone,
        idCard: tenant.idCard,
        status: tenant.status
    };

    drawerVisible.value = true;
};

// 删除租户
const handleDelete = (tenant: Tenant) => {
    const confirm = DialogPlugin.confirm({
        header: '确认删除',
        body: `确定要删除租户"${tenant.name}"吗？删除后数据不可恢复！`,
        theme: 'danger',
        confirmBtn: {
            content: '确认删除',
            theme: 'danger',
        },
        cancelBtn: {
            content: '取消',
            theme: 'default',
        },
        onConfirm: async () => {
            try {
                const { post } = useHttp();
                await post('/api/tenants/delete', { id: tenant.id });

                MessagePlugin.success('删除成功');
                fetchTenants();
            } catch (error) {
                MessagePlugin.error('删除失败');
            } finally {
                confirm.hide();
            }
        }
    });
};

// 关闭抽屉
const onDrawerClose = () => {
    drawerVisible.value = false;
    formData.value = {
        id: null,
        name: '',
        phone: '',
        idCard: '',
        status: 1
    };
    isEdit.value = false;
    // 重置表单验证状态
    formRef.value?.reset();
};

// 手动触发表单验证
const handleSubmit = () => {
    formRef.value?.submit();
};

// 提交表单
const onSubmit = async ({ validateResult, firstError }: { validateResult: boolean, firstError: string }) => {
    if (validateResult !== true) {
        MessagePlugin.error(firstError);
        return;
    }

    try {
        submitting.value = true;
        const { post } = useHttp();
        const url = isEdit.value ? '/api/tenants/update' : '/api/tenants/save';
        await post(url, formData.value);
        
        MessagePlugin.success(isEdit.value ? '更新成功' : '创建成功');
        drawerVisible.value = false;
        fetchTenants();
    } catch (error) {
        MessagePlugin.error(isEdit.value ? '更新失败' : '创建失败');
    } finally {
        submitting.value = false;
    }
};

// 页面加载时获取租户列表
onMounted(() => {
    fetchTenants();
});
</script>

<style scoped>
.tenants-container {
    padding: 20px;
}
</style>