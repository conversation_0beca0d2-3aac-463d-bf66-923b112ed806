<template>
  <div>
    <div class="page-header">
      <h2>用户管理</h2>
      <PermissionGuard permission="user:create">
        <button class="add-btn">+ 添加用户</button>
      </PermissionGuard>
    </div>

    <div class="users-table">
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>用户名</th>
            <th>邮箱</th>
            <th>角色</th>
            <th>注册时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(user, index) in users" :key="index">
            <td>{{ user.id }}</td>
            <td>{{ user.username }}</td>
            <td>{{ user.email }}</td>
            <td>{{ user.role }}</td>
            <td>{{ user.registerDate }}</td>
            <td class="actions">
              <PermissionGuard permission="user:edit">
                <button class="edit-btn">编辑</button>
              </PermissionGuard>
              <PermissionGuard permission="user:delete">
                <button class="delete-btn">删除</button>
              </PermissionGuard>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="pagination">
      <button class="page-btn">&lt;</button>
      <button class="page-btn active">1</button>
      <button class="page-btn">2</button>
      <button class="page-btn">3</button>
      <button class="page-btn">&gt;</button>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'dashboard'
});

// 模拟用户数据
const users = [
  {
    id: 1001,
    username: '张三',
    email: '<EMAIL>',
    role: '管理员',
    registerDate: '2023-01-15'
  },
  {
    id: 1002,
    username: '李四',
    email: '<EMAIL>',
    role: '用户',
    registerDate: '2023-02-20'
  },
  {
    id: 1003,
    username: '王五',
    email: '<EMAIL>',
    role: '用户',
    registerDate: '2023-03-10'
  },
  {
    id: 1004,
    username: '赵六',
    email: '<EMAIL>',
    role: '编辑',
    registerDate: '2023-04-05'
  },
  {
    id: 1005,
    username: '钱七',
    email: '<EMAIL>',
    role: '用户',
    registerDate: '2023-05-17'
  }
];
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
}

.add-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.users-table {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 24px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f9f9f9;
  font-weight: 600;
}

.actions {
  display: flex;
  gap: 8px;
}

.edit-btn, .delete-btn {
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
}

.edit-btn {
  background-color: #2196f3;
  color: white;
}

.delete-btn {
  background-color: #f44336;
  color: white;
}

.pagination {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.page-btn {
  width: 36px;
  height: 36px;
  border: 1px solid #ddd;
  background-color: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.page-btn.active {
  background-color: #1e1e2d;
  color: white;
  border-color: #1e1e2d;
}
</style> 