<template>
  <div class="login-container relative">
    <div class="login-card">
      <div class="login-content flex flex-col gap-2">
        <h1 class="welcome-title">盂县联众种植专业合作社</h1>
        <p class="login-subtitle">登录到您的工作台</p>
        <div class="form-container flex-1 flex flex-col gap-2">
          <div class="form-item">
            <label>账号</label>
            <t-input v-model="phone" placeholder="请输入账号" size="large" />
          </div>
          <div class="form-item">
            <div class="password-header">
              <label>密码</label>
            </div>
            <t-input v-model="password" type="password" size="large" />
          </div>
          <t-button theme="primary" block size="large" @click="handleLogin">登录</t-button>
        </div>
      </div>

      <!-- 右侧图像区域 -->
      <div class="image-container">
        <div class="placeholder-image">
          <img src="@/assets/images/login_bg3.png" alt="login" />
        </div>
      </div>
    </div>

      <div class="text-center text-sm text-gray-500 absolute bottom-8 left-1/2 -translate-x-1/2">
        <!-- <a href="https://beian.miit.gov.cn/" target="_blank" class="text-gray-500">豫ICP备2025128098号-1</a> -->
      </div>
  </div>
</template>

<script setup>
import { MessagePlugin } from 'tdesign-vue-next';
import { ref } from 'vue';

const phone = ref('');
const password = ref('');
const { login } = useAuth();

const handleLogin = async () => {
  if (!phone.value || !password.value) {
    MessagePlugin.error('请输入手机号和密码');
    return;
  }
  await login({
    phone: phone.value,
    password: password.value,
  })
};
</script>

<style scoped>
.login-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.login-card {
  display: flex;
  width: 100%;
  max-width: 900px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.login-content {
  width: 50%;
  padding: 40px;
}

.image-container {
  width: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-image {
  color: #d9d9d9;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

.login-subtitle {
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 32px;
}

.form-container {
  width: 100%;
}

.form-item {
  margin-bottom: 24px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.password-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.divider {
  position: relative;
  text-align: center;
  margin: 24px 0;
}

.divider::before,
.divider::after {
  content: '';
  position: absolute;
  top: 50%;
  width: calc(50% - 80px);
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
}

.divider::before {
  left: 0;
}

.divider::after {
  right: 0;
}

.divider span {
  display: inline-block;
  padding: 0 16px;
  background-color: white;
  position: relative;
  color: rgba(0, 0, 0, 0.6);
}

.social-login {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
}

.signup-link {
  text-align: center;
  margin-top: 16px;
}

.terms-footer {
  margin-top: 24px;
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .login-card {
    flex-direction: column;
  }

  .login-content,
  .image-container {
    width: 100%;
  }

  .image-container {
    min-height: 200px;
    order: -1;
  }
}
</style>