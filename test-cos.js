import COS from 'cos-nodejs-sdk-v5'
import dotenv from 'dotenv'

// 加载环境变量
dotenv.config()

// 验证环境变量
const requiredEnvs = ['COS_SECRET_ID', 'COS_SECRET_KEY', 'COS_BUCKET', 'COS_REGION']
const missingEnvs = requiredEnvs.filter(env => !process.env[env])

if (missingEnvs.length > 0) {
  console.error('❌ 缺少必要的环境变量:', missingEnvs)
  console.log('\n请在 .env 文件中添加以下配置:')
  console.log('COS_SECRET_ID="your-secret-id"')
  console.log('COS_SECRET_KEY="your-secret-key"')
  console.log('COS_BUCKET="your-bucket-name-1250000000"')
  console.log('COS_REGION="ap-beijing"')
  process.exit(1)
}

console.log('✅ 环境变量检查通过')
console.log('配置信息:')
console.log(`- 存储桶: ${process.env.COS_BUCKET}`)
console.log(`- 地域: ${process.env.COS_REGION}`)
console.log(`- SecretId: ${process.env.COS_SECRET_ID?.substring(0, 8)}...`)

// 初始化COS客户端
const cos = new COS({
  SecretId: process.env.COS_SECRET_ID,
  SecretKey: process.env.COS_SECRET_KEY,
})

// 测试连接
async function testCOSConnection() {
  try {
    console.log('\n🔄 测试COS连接...')
    
    // 测试获取存储桶信息
    const result = await new Promise((resolve, reject) => {
      cos.headBucket({
        Bucket: process.env.COS_BUCKET,
        Region: process.env.COS_REGION,
      }, (err, data) => {
        if (err) {
          reject(err)
        } else {
          resolve(data)
        }
      })
    })
    
    console.log('✅ COS连接成功!')
    console.log('存储桶状态:', result)
    
    // 测试上传一个小文件
    console.log('\n🔄 测试文件上传...')
    const testContent = 'Hello COS! 测试文件内容'
    const testKey = 'test/test-file.txt'
    
    const uploadResult = await new Promise((resolve, reject) => {
      cos.putObject({
        Bucket: process.env.COS_BUCKET,
        Region: process.env.COS_REGION,
        Key: testKey,
        Body: testContent,
        ContentType: 'text/plain',
      }, (err, data) => {
        if (err) {
          reject(err)
        } else {
          resolve(data)
        }
      })
    })
    
    console.log('✅ 文件上传成功!')
    console.log('文件URL:', `https://${process.env.COS_BUCKET}.cos.${process.env.COS_REGION}.myqcloud.com/${testKey}`)
    
    // 清理测试文件
    console.log('\n🔄 清理测试文件...')
    await new Promise((resolve, reject) => {
      cos.deleteObject({
        Bucket: process.env.COS_BUCKET,
        Region: process.env.COS_REGION,
        Key: testKey,
      }, (err, data) => {
        if (err) {
          reject(err)
        } else {
          resolve(data)
        }
      })
    })
    
    console.log('✅ 测试文件已清理')
    console.log('\n🎉 腾讯云COS配置测试完成，一切正常!')
    
  } catch (error) {
    console.error('\n❌ COS测试失败:', error.message)
    if (error.code) {
      console.error('错误代码:', error.code)
    }
    if (error.statusCode) {
      console.error('HTTP状态码:', error.statusCode)
    }
    
    console.log('\n💡 常见问题排查:')
    console.log('1. 检查SecretId和SecretKey是否正确')
    console.log('2. 检查存储桶名称是否包含APPID (格式: bucketname-appid)')
    console.log('3. 检查地域代码是否正确')
    console.log('4. 确认API密钥具有COS操作权限')
    console.log('5. 检查存储桶是否存在且可访问')
  }
}

testCOSConnection() 