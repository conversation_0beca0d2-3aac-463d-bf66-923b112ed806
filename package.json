{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "build:production": "nuxt build --dotenv .env.production", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "db:seed": "node ./server/db/seed.ts", "setup": "npm install && npm run db:seed && npm run dev", "deploy": "wrangler pages deploy dist/ --project-name=yilanjiancai-app", "test:cos": "node test-cos.js"}, "dependencies": {"@nuxt/icon": "1.12.0", "@nuxt/image": "1.10.0", "@tailwindcss/vite": "^4.1.6", "@tdesign-vue-next/nuxt": "0.1.5", "@vueup/vue-quill": "^1.2.0", "bcryptjs": "^3.0.2", "cos-nodejs-sdk-v5": "2.16.0-beta.3", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "nanoid": "^5.1.5", "nuxt": "^3.17.2", "quill": "^2.0.3", "tailwindcss": "^4.1.6", "tdesign-icons-vue-next": "^0.3.6", "tdesign-vue-next": "^1.12.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "xlsx": "^0.18.5", "zod": "^3.24.4"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39", "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/nprogress": "^0.2.3", "drizzle-kit": "^0.31.1", "nprogress": "^0.2.0", "tsx": "^4.19.4", "wrangler": "^4.14.4"}}