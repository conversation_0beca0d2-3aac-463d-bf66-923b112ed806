<template>
  <div v-if="isAuthorized">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue';
import { usePermission } from '~/composables/usePermission';

const props = defineProps({
  // 必须拥有的权限
  permission: {
    type: String,
    default: '',
  },
  // 至少拥有其中一个权限
  anyPermission: {
    type: Array as () => string[],
    default: () => [],
  },
  // 必须拥有所有权限
  allPermissions: {
    type: Array as () => string[],
    default: () => [],
  },
  // 必须是指定角色
  role: {
    type: String,
    default: '',
  },
  // 至少是其中一个角色
  anyRole: {
    type: Array as () => string[],
    default: () => [],
  },
});

const { hasPermission, hasAnyPermission, hasAllPermissions, hasRole, hasAnyRole } = usePermission();

// 计算是否有权限访问
const isAuthorized = computed(() => {
  // 按照优先级检查
  if (props.permission && !hasPermission(props.permission)) {
    return false;
  }
  
  if (props.anyPermission.length > 0 && !hasAnyPermission(props.anyPermission)) {
    return false;
  }
  
  if (props.allPermissions.length > 0 && !hasAllPermissions(props.allPermissions)) {
    return false;
  }
  
  if (props.role && !hasRole(props.role)) {
    return false;
  }
  
  if (props.anyRole.length > 0 && !hasAnyRole(props.anyRole)) {
    return false;
  }
  
  // 如果没有指定任何检查条件，默认显示
  if (!props.permission && props.anyPermission.length === 0 && 
      props.allPermissions.length === 0 && !props.role && props.anyRole.length === 0) {
    return true;
  }
  
  return true;
});
</script> 