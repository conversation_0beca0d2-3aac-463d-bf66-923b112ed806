<template>
  <client-only>
    <t-menu theme="light" :value="activeMenu" :expanded="expandedMenus" @expand="handleExpand" @change="handleChange">
      <template v-for="(menu, index) in filteredMenus" :key="index">
        <!-- 无子菜单的菜单项 -->
        <t-menu-item v-if="!menu.children || menu.children.length === 0" :value="menu.value">
          <template #icon>
            <t-icon :name="menu.icon" />
          </template>
          {{ menu.label }}
        </t-menu-item>

        <!-- 有子菜单的菜单项 -->
        <t-submenu v-else :value="menu.value">
          <template #icon>
            <t-icon :name="menu.icon" />
          </template>
          <template #title>{{ menu.label }}</template>
          <t-menu-item v-for="(subMenu, subIndex) in menu.children" :key="`${index}-${subIndex}`"
            :value="subMenu.value">
            <template #icon v-if="subMenu.icon">
              <t-icon :name="subMenu.icon" />
            </template>
            {{ subMenu.label }}
          </t-menu-item>
        </t-submenu>
      </template>
    </t-menu>
  </client-only>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useAuth } from '~/composables/userAuth';
// 引入TDesign类型
import type { MenuValue } from 'tdesign-vue-next';

// 菜单项定义
interface MenuItem {
  value: string;
  label: string;
  icon?: string;
  children?: MenuItem[];
  path?: string;
  permission?: string; // 所需权限
}

// 菜单数据
const menus: MenuItem[] = [
  {
    value: 'dashboard',
    label: '首页',
    icon: 'dashboard',
    path: '/dashboard'
    // 首页不需要权限
  },
  {
    label: "地块管理",
    value: "land",
    icon: "map-location",
    path: "/dashboard/land",
    //permission: "land:menu",
  },
  {
    label: "作物管理",
    value: "crop",
    icon: "apple",
    path: "/dashboard/crop",
    //permission: "crop:menu",
  },
  {
    label: "租户管理",
    value: "tenant",
    icon: "usergroup",
    path: "/dashboard/tenant",
    //permission: "tenant:menu",
  },
  {
    label: "合同管理",
    value: "contract",
    icon: "git-repository",
    path: "/dashboard/contract",
    //permission: "contract:menu",
  },
  {
    label: "订单管理",
    value: "order",
    icon: "catalog",
    path: "/dashboard/order",
    //permission: "order:menu",
  },
  {
    label: "新闻管理",
    value: "news",
    icon: "course",
    path: "/dashboard/news",
    //permission: "news:menu",
  },
  {
    label: "照片管理",
    value: "photo",
    icon: "image",
    path: "/dashboard/photo",
    //permission: "photo:menu",
  },
  {
    value: 'system-settings',
    label: '系统设置',
    icon: 'setting',
    permission: 'system:menu',
    children: [
      {
        value: 'user-list',
        label: '用户列表',
        path: '/dashboard/sys/users',
        permission: 'user:menu'
      },
     /*  {
        value: 'user-role',
        label: '角色管理',
        path: '/dashboard/sys/roles',
        permission: 'role:menu'
      },
      {
        value: 'log',
        label: '操作日志',
        path: '/dashboard/sys/log',
        permission: 'log:menu'
      } */
    ]
  }
];

// 获取当前用户权限
const { user } = useAuth();
const userPermissions = computed(() => {
  return user.value?.permissions || [];
});

// 检查用户是否有权限
const hasPermission = (permission?: string): boolean => {
  if (!permission) return true; // 没有权限要求的菜单项默认显示
  return userPermissions.value.includes(permission);
};

// 过滤菜单项
const filterMenuByPermissions = (menuItems: MenuItem[]): MenuItem[] => {
  return menuItems.filter(item => {
    // 检查当前菜单项权限
    if (!hasPermission(item.permission)) {
      return false;
    }

    // 如果有子菜单，递归过滤子菜单
    if (item.children && item.children.length > 0) {
      const filteredChildren = filterMenuByPermissions(item.children);
      // 如果过滤后没有子菜单，则不显示父菜单
      if (filteredChildren.length === 0) {
        return false;
      }
      // 更新子菜单
      item.children = filteredChildren;
    }

    return true;
  });
};

// 根据权限过滤后的菜单
const filteredMenus = computed(() => {
  return filterMenuByPermissions([...menus]);
});

// 动态生成路由路径到菜单值的映射
const routeToMenuMap = computed(() => {
  const map: Record<string, string> = {};

  const processMenuItem = (item: MenuItem) => {
    if (item.path) {
      map[item.path] = item.value;
    }

    if (item.children) {
      item.children.forEach(processMenuItem);
    }
  };

  filteredMenus.value.forEach(processMenuItem);

  return map;
});

// 动态生成子菜单到父菜单的映射
const parentMenuMap = computed(() => {
  const map: Record<string, string> = {};

  filteredMenus.value.forEach(parent => {
    if (parent.children && parent.children.length > 0) {
      parent.children.forEach(child => {
        map[child.value] = parent.value;
      });
    }
  });

  return map;
});

// 获取当前路由
const route = useRoute();

// 获取当前路由路径，确保末尾没有斜杠
const getCurrentPath = (): string => {
  let path = route.path;
  // 如果路径以斜杠结尾且长度大于1，则去掉结尾的斜杠
  if (path.endsWith('/') && path.length > 1) {
    path = path.slice(0, -1);
  }
  return path;
};

// 根据路由路径获取菜单值
const getMenuValueFromRoute = (path: string): string => {
  return routeToMenuMap.value[path] || 'dashboard';
};

// 根据菜单值获取父菜单
const getParentMenu = (menuValue: string): string | null => {
  return parentMenuMap.value[menuValue] || null;
};

// 活动菜单
const activeMenu = ref<MenuValue>('dashboard');

// 展开的菜单
const expandedMenus = ref<MenuValue[]>([]);

// 更新当前活动菜单和展开菜单
const updateActiveMenu = () => {
  const currentPath = getCurrentPath();
  const menuValue = getMenuValueFromRoute(currentPath);
  activeMenu.value = menuValue;

  // 更新展开的菜单
  const parentMenu = getParentMenu(menuValue as string);
  if (parentMenu) {
    // 确保父菜单在展开列表中
    if (!expandedMenus.value.includes(parentMenu)) {
      expandedMenus.value = [parentMenu];
    }
  }
};

// 监听路由变化，更新活动菜单
watch(() => route.path, () => {
  updateActiveMenu();
}, { immediate: true });

// 初始化时设置活动菜单
onMounted(() => {
  updateActiveMenu();
});

// 处理菜单展开事件
const handleExpand = (value: MenuValue[]) => {
  expandedMenus.value = value as MenuValue[];
};

// 菜单变更处理
const handleChange = (value: MenuValue) => {
  activeMenu.value = value;

  // 如果点击的是有子菜单的父菜单项
  const isParentMenu = filteredMenus.value.some(menu => menu.value === value && menu.children && menu.children.length > 0);
  if (isParentMenu) {
    // 如果已经展开，则收起；如果收起，则展开
    if (expandedMenus.value.includes(value)) {
      expandedMenus.value = expandedMenus.value.filter(item => item !== value);
    } else {
      expandedMenus.value = [...expandedMenus.value, value];
    }
    return; // 不进行导航
  }

  // 获取要导航到的路径
  let path = '/dashboard';

  // 查找菜单项的路径
  const findMenuItemPath = (items: MenuItem[], value: string): string | undefined => {
    for (const item of items) {
      if (item.value === value) {
        return item.path;
      }
      if (item.children) {
        const childPath = findMenuItemPath(item.children, value);
        if (childPath) return childPath;
      }
    }
    return undefined;
  };

  const targetPath = findMenuItemPath(filteredMenus.value, String(value));
  if (targetPath) {
    path = targetPath;
  }

  // 导航到对应路径
  navigateTo(path);
};
</script>

<style scoped>
.t-menu {
  width: 100%;
  height: auto !important;
  max-height: none !important;
}

/* 确保菜单项可以正常显示 */
:deep(.t-menu__item),
:deep(.t-submenu) {
  flex-shrink: 0;
}

/* 确保子菜单容器不会被截断 */
:deep(.t-menu__content) {
  height: auto !important;
  max-height: none !important;
}
</style>
