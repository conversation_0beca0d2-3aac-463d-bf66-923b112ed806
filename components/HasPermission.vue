<template>
  <div v-if="hasAccess">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { usePermission } from '~/composables/usePermission';

interface Props {
  permission?: string;
  anyPermission?: string[];
  allPermissions?: string[];
  role?: string;
  anyRole?: string[];
}

const props = withDefaults(defineProps<Props>(), {
  permission: '',
  anyPermission: () => [],
  allPermissions: () => [],
  role: '',
  anyRole: () => [],
});

const { hasPermission, hasAnyPermission, hasAllPermissions, hasRole, hasAnyRole } = usePermission();

// 计算是否有访问权限
const hasAccess = computed(() => {
  // 如果指定了单个权限，检查该权限
  if (props.permission) {
    return hasPermission(props.permission);
  }
  
  // 如果指定了任意权限数组，检查是否拥有其中任意一个
  if (props.anyPermission.length > 0) {
    return hasAnyPermission(props.anyPermission);
  }
  
  // 如果指定了所有权限数组，检查是否拥有所有权限
  if (props.allPermissions.length > 0) {
    return hasAllPermissions(props.allPermissions);
  }
  
  // 如果指定了单个角色，检查该角色
  if (props.role) {
    return hasRole(props.role);
  }
  
  // 如果指定了任意角色数组，检查是否是其中任意一个角色
  if (props.anyRole.length > 0) {
    return hasAnyRole(props.anyRole);
  }
  
  // 如果没有指定任何权限/角色要求，默认显示
  return true;
});
</script> 