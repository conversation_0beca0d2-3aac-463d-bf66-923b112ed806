DATABASE_URL="mysql://root:hello,password!@127.0.0.1:3308/yuxian_farm"
DATABASE_HOST="127.0.0.1"
DATABASE_PORT="3308"
DATABASE_USER="root"
DATABASE_PASSWORD="hello,password!"
DATABASE_NAME="yuxian_farm"

JWT_SECRET="3emMqO5RwsooKsHxDAWoEHw8JHZJcKkvd4e3a8mWhlL75oDVRHkR7743934Vh0q7J"
JWT_EXPIRES_IN="7d"

# 腾讯云COS 配置
COS_SECRET_ID="AKIDSnvrcCe4Zx8NWSN5dJtJah0J6ofQEsNk"        # 腾讯云API密钥SecretId
COS_SECRET_KEY="HQQGyiNF3hCXgcbaMuGQPFwhMmAXls17"      # 腾讯云API密钥SecretKey
COS_BUCKET="yuxian-farm-1256165049"         # COS存储桶名称（需要包含APPID，如：examplebucket-1250000000）
COS_REGION="ap-beijing"               # COS存储桶所在地域