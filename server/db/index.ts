import { drizzle } from "drizzle-orm/mysql2";
import mysql from "mysql2";
import * as schema from "./schema";

// 获取运行时配置
const config = useRuntimeConfig();

const poolConnection = mysql.createPool({
  host: config.databaseHost,
  user: config.databaseUser,
  password: config.databasePassword,
  database: config.databaseName,
  port: config.databasePort ? parseInt(config.databasePort) : 3306,
  timezone: '+08:00',
});

const db = drizzle(poolConnection, { schema, mode: "default", logger: true });

export default db;
