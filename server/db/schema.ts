import {
  mysqlTable,
  serial,
  varchar,
  timestamp,
  int,
  primaryKey,
  text,
  decimal,
  longtext,
} from "drizzle-orm/mysql-core";
import { relations } from "drizzle-orm";
import { sql } from "drizzle-orm";
import { nanoid } from "nanoid";
// 用户表
export const users = mysqlTable("users", {
  id: serial("id").primaryKey(),
  username: varchar("username", { length: 255 }).notNull(),
  phone: varchar("phone", { length: 255 }).notNull(),
  password: varchar("password", { length: 255 }).notNull(),
  roleId: int("role_id").notNull(), // 添加角色ID
  status: int("status").notNull().default(1), // 添加状态字段 1: 正常 2: 禁用
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`),
});

// 角色表
export const roles = mysqlTable("roles", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 50 }).notNull().unique(),
  description: varchar("description", { length: 255 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`),
});

// 权限表
export const permissions = mysqlTable("permissions", {
  id: serial("id").primaryKey(),
  group: varchar("group", { length: 50 }).notNull(),
  name: varchar("name", { length: 50 }).notNull(),
  code: varchar("code", { length: 50 }).notNull().unique(), // 权限编码，例如 'user:create'
  type: varchar("type", { length: 20 }).notNull().default("button"), // 权限类型：menu(菜单权限) | button(按钮权限)
  parentId: int("parent_id"), // 父权限ID，形成树形结构
  sort: int("sort").notNull().default(0), // 排序字段
  description: varchar("description", { length: 255 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`),
});

// 角色-权限关联表
export const rolePermissions = mysqlTable(
  "role_permissions",
  {
    roleId: int("role_id").notNull(),
    permissionId: int("permission_id").notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.roleId, table.permissionId] }),
  })
);

// 操作日志表
export const operationLogs = mysqlTable("operation_logs", {
  id: serial("id").primaryKey(),
  module: varchar("module", { length: 50 }).notNull(), // 模块名称，如：supplier, user, role 等
  action: varchar("action", { length: 50 }).notNull(), // 操作类型，如：create, update, delete
  operatorId: varchar("operator_id", { length: 50 }).notNull(), // 操作人ID
  operatorName: varchar("operator_name", { length: 50 }).notNull(), // 操作人姓名
  targetId: varchar("target_id", { length: 50 }).notNull(), // 操作对象ID
  targetType: varchar("target_type", { length: 50 }).notNull(), // 操作对象类型
  oldData: text("old_data"), // 修改前的数据（JSON格式）
  newData: text("new_data"), // 修改后的数据（JSON格式）
  ip: varchar("ip", { length: 50 }), // 操作人IP
  userAgent: varchar("user_agent", { length: 255 }), // 操作人浏览器信息
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`),
});

// 定义关系
export const usersRelations = relations(users, ({ one }) => ({
  role: one(roles, {
    fields: [users.roleId],
    references: [roles.id],
  }),
}));

export const rolesRelations = relations(roles, ({ many }) => ({
  users: many(users),
  rolePermissions: many(rolePermissions),
}));

export const permissionsRelations = relations(permissions, ({ many, one }) => ({
  rolePermissions: many(rolePermissions),
  parent: one(permissions, {
    fields: [permissions.parentId],
    references: [permissions.id],
    relationName: "parentChild",
  }),
  children: many(permissions, {
    relationName: "parentChild",
  }),
}));

export const rolePermissionsRelations = relations(
  rolePermissions,
  ({ one }) => ({
    role: one(roles, {
      fields: [rolePermissions.roleId],
      references: [roles.id],
    }),
    permission: one(permissions, {
      fields: [rolePermissions.permissionId],
      references: [permissions.id],
    }),
  })
);

// 添加关系
export const operationLogsRelations = relations(operationLogs, ({ one }) => ({
  operator: one(users, {
    fields: [operationLogs.operatorId],
    references: [users.id],
  }),
}));

//---------- 业务表

// 地块表
export const lands = mysqlTable("lands", {
  id: varchar("id", { length: 10 })
    .primaryKey()
    .$defaultFn(() => nanoid(10)),
  name: varchar("name", { length: 255 }).notNull(), // 地块名称
  area: decimal("area", { precision: 10, scale: 2 }).notNull(), // 地块面积
  status: int("status").notNull().default(1), //  1已租 2 未租
  createdAt: varchar("created_at", { length: 50 }).default(""), // 创建时间 东八区时间
  updatedAt: varchar("updated_at", { length: 50 }).default(""), // 更新时间 东八区时间
});

//作物表（地块中种植的作物）
export const crops = mysqlTable("crops", {
  id: varchar("id", { length: 10 })
    .primaryKey()
    .$defaultFn(() => nanoid(10)),
  name: varchar("name", { length: 255 }).notNull(), // 作物名称
  emoji: varchar("emoji", { length: 255 }).notNull(), // 作物图标
  landId: varchar("land_id", { length: 10 }).notNull(), // 地块ID
  plantingArea: decimal("planting_area", { precision: 10, scale: 2 }).notNull(), // 种植面积
  plantingDate: timestamp("planting_date").notNull(), // 种植日期
  expectedHarvestDate: timestamp("expected_harvest_date").notNull(), // 预计收获日期
  actualHarvestDate: timestamp("actual_harvest_date"), // 实际收获日期
  status: int("status").notNull().default(1), // 作物状态 1: 生长中 2: 已成熟 3: 已收获
  createdAt: varchar("created_at", { length: 50 }).default(""), // 创建时间 东八区时间
  updatedAt: varchar("updated_at", { length: 50 }).default(""), // 更新时间 东八区时间
});

//作物信息表
export const cropsInfo = mysqlTable("crops_info", {
  id: varchar("id", { length: 10 })
    .primaryKey()
    .$defaultFn(() => nanoid(10)),
  emoji: varchar("emoji", { length: 255 }).notNull(), // 作物图标
  name: varchar("name", { length: 255 }).notNull(), // 作物名称
  growthDays: int("growth_days").notNull(), // 成熟天数
  remarks: text("remarks"), // 备注
});

//合同表
export const contracts = mysqlTable("contracts", {
  id: varchar("id", { length: 10 })
    .primaryKey()
    .$defaultFn(() => nanoid(10)),
  contractNumber: varchar("contract_number", { length: 255 }).notNull(), // 合同编号
  contractStartDate: timestamp("contract_start_date").notNull(), // 合同开始日期
  contractEndDate: timestamp("contract_end_date").notNull(), // 合同结束日期
  contractAmount: decimal("contract_amount", { precision: 10, scale: 2 }).notNull(), // 合同金额
  contractStatus: int("contract_status").notNull().default(1), // 合同状态 1: 正常 2: 已过期
  tenantId: varchar("tenant_id", { length: 10 }).notNull(), // 租户ID
  createdAt: varchar("created_at", { length: 50 }).default(""), // 创建时间 东八区时间
  updatedAt: varchar("updated_at", { length: 50 }).default(""), // 更新时间 东八区时间
});

//租户表
export const tenants = mysqlTable("tenants", {
  id: varchar("id", { length: 10 })
    .primaryKey()
    .$defaultFn(() => nanoid(10)),
  name: varchar("name", { length: 255 }).notNull(), // 租户姓名
  phone: varchar("phone", { length: 255 }).notNull(), // 租户电话
  idCard: varchar("id_card", { length: 255 }).notNull(), // 租户身份证号
  status: int("status").notNull().default(1), // 租户状态 1: 正常 2: 禁用
  createdAt: varchar("created_at", { length: 50 }).default(""), // 创建时间 东八区时间
  updatedAt: varchar("updated_at", { length: 50 }).default(""), // 更新时间 东八区时间
});

//合同地块关系表
export const contractLands = mysqlTable("contract_lands", {
  id: varchar("id", { length: 10 })
    .primaryKey()
    .$defaultFn(() => nanoid(10)),
  contractId: varchar("contract_id", { length: 10 }).notNull(), // 合同ID
  landId: varchar("land_id", { length: 10 }).notNull(), // 地块ID
  tenantId: varchar("tenant_id", { length: 10 }).notNull(), // 租户ID
  createdAt: varchar("created_at", { length: 50 }).default(""), // 创建时间 东八区时间
  updatedAt: varchar("updated_at", { length: 50 }).default(""), // 更新时间 东八区时间
});

//订单表
export const orders = mysqlTable("orders", {
  id: varchar("id", { length: 10 })
    .primaryKey()
    .$defaultFn(() => nanoid(10)),
  orderNumber: varchar("order_number", { length: 255 }).notNull(), // 订单编号
  orderDate: timestamp("order_date").notNull(), // 订单日期
  tenantId: varchar("tenant_id", { length: 10 }).notNull(), // 租户ID
  cropId: varchar("crop_id", { length: 10 }).notNull(), // 作物ID
  landId: varchar("land_id", { length: 10 }).notNull(), // 地块ID
  receiverName: varchar("receiver_name", { length: 255 }).notNull(), // 收货人姓名
  receiverPhone: varchar("receiver_phone", { length: 255 }).notNull(), // 收货人电话
  receiverAddress: varchar("receiver_address", { length: 255 }).notNull(), // 收货人地址
  logisticsCompany: varchar("logistics_company", { length: 255 }).notNull(), // 物流公司
  logisticsNumber: varchar("logistics_number", { length: 255 }).notNull(), // 物流单号
  orderStatus: int("order_status").notNull().default(1), // 订单状态 1: 待处理 2: 已发货 3: 已收货 4: 已取消 5: 已取消
  createdAt: varchar("created_at", { length: 50 }).default(""), // 创建时间 东八区时间
  updatedAt: varchar("updated_at", { length: 50 }).default(""), // 更新时间 东八区时间
});

//新闻表
export const news = mysqlTable("news", {
  id: varchar("id", { length: 10 })
    .primaryKey()
    .$defaultFn(() => nanoid(10)),
  title: varchar("title", { length: 255 }).notNull(), // 新闻标题
  content: longtext("content").notNull(), // 新闻内容
  createdAt: varchar("created_at", { length: 50 }).default(""), // 创建时间 东八区时间
  updatedAt: varchar("updated_at", { length: 50 }).default(""), // 更新时间 东八区时间
});

//照片表
export const photos = mysqlTable("photos", {
  id: varchar("id", { length: 10 })
    .primaryKey()
    .$defaultFn(() => nanoid(10)),
  name: varchar("name", { length: 255 }).notNull(), // 照片名称
  url: text("url").notNull(), // 照片URL
  landId: varchar("land_id", { length: 10 }).notNull(), // 地块ID
  cropId: varchar("crop_id", { length: 10 }).notNull(), // 作物ID
  tenantId: varchar("tenant_id", { length: 10 }).notNull(), // 租户ID
  createdAt: varchar("created_at", { length: 50 }).default(""), // 创建时间 东八区时间
  updatedAt: varchar("updated_at", { length: 50 }).default(""), // 更新时间 东八区时间
});