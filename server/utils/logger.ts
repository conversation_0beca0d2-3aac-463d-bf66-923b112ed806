import db from '~/server/db';
import { operationLogs } from '~/server/db/schema';
import type { H3Event } from 'h3';
import type { AuthContext } from '../types/auth';

interface LogData {
    module: string;
    action: string;
    targetId: string;
    targetType: string;
    oldData?: any;
    newData?: any;
}

export async function createOperationLog(event: H3Event, data: LogData) {
    try {
        const auth = event.context.auth as AuthContext;
        const headers = getHeaders(event);

        await db.insert(operationLogs).values({
            module: data.module,
            action: data.action,
            operatorId: auth?.user?.id || '0',
            operatorName: auth?.user?.name || 'system',
            targetId: data.targetId,
            targetType: data.targetType,
            oldData: data.oldData ? JSON.stringify(data.oldData) : null,
            newData: data.newData ? JSON.stringify(data.newData) : null,
            ip: headers['x-forwarded-for'] || headers['x-real-ip'] || '',
            userAgent: headers['user-agent'] || '',
        });
    } catch (error) {
        console.error('Failed to create operation log:', error);
    }
}