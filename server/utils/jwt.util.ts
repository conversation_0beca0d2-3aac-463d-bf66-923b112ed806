// server/utils/jwt.ts
import jwt, { SignOptions } from 'jsonwebtoken';

const config = useRuntimeConfig();

if (!config.jwtSecret) {
  throw new Error('JWT_SECRET is not defined in runtimeConfig. Check your .env and nuxt.config.ts');
}

const JWT_SECRET = config.jwtSecret as string;
const JWT_EXPIRES_IN = config.jwtExpiresIn as string;

interface JwtPayload {
  userId: number;
  name: string;
  phone: string;
  roleId: number;
  roleName: string;
  permissions?: string[];
}

export function generateToken(payload: JwtPayload): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN } as SignOptions);
}

export function verifyToken(token: string): JwtPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JwtPayload;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}