// server/middleware/auth-jwt.ts
// (注意：如果全局应用此中间件，它会检查所有API请求，可能不是你想要的)
// 通常你会选择性地将这类检查逻辑放在需要保护的API端点内部，或者使用Nitro的路由规则。

import { verifyToken } from '~/server/utils/jwt.util';

// API权限映射
interface ApiPermissionMap {
  [key: string]: string | string[];
}

const apiPermissionMap: ApiPermissionMap = {
  // 订单管理API
  '/api/orders/list': ['order:view'],
  '/api/orders/export': ['order:export', 'customer:order:export', 'supplier:order:export', 'fleet:order:export'], // 支持多个模块导出订单
  '/api/orders/save': ['order:create'],
  '/api/orders/update': ['order:edit'],
  '/api/orders/detail': ['order:view'],
  '/api/orders/batch-import': ['order:batch-import'],
  '/api/orders/delete': ['order:delete'],
  
  // 客户管理API
  '/api/customers/list': ['customer:view'],
  '/api/customers/save': ['customer:create'],
  '/api/customers/update': ['customer:edit'],
  '/api/customers/delete': ['customer:delete'],
  
  // 供应商管理API
  '/api/suppliers/list': ['supplier:view'],
  '/api/suppliers/save': ['supplier:create'],
  '/api/suppliers/update': ['supplier:edit'],
  '/api/suppliers/delete': ['supplier:delete'],
  
  // 产品管理API
  '/api/products/list': ['product:view'],
  '/api/products/save': ['product:create'],
  '/api/products/update': ['product:edit'],
  '/api/products/delete': ['product:delete'],
  
  // 车队管理API
  '/api/fleets/list': ['fleet:view'],
  '/api/fleets/save': ['fleet:create'],
  '/api/fleets/update': ['fleet:edit'],
  '/api/fleets/delete': ['fleet:delete'],
  '/api/fleets/vehicles-list': ['fleet:view'],
  '/api/fleets/options': ['*'],
  
  // 车队车辆管理API
  '/api/fleets/vehicles/list': ['fleet:vehicle:view'],
  '/api/fleets/vehicles/save': ['fleet:vehicle:create'],
  '/api/fleets/vehicles/update': ['fleet:vehicle:edit'],
  '/api/fleets/vehicles/delete': ['fleet:vehicle:delete'],
  
  // 交易流水管理API
  '/api/transactions/list': ['transaction:view', 'transaction:customer:view', 'transaction:fleet:view'],
  '/api/transactions/detail': ['transaction:view', 'customer:transaction:view', 'supplier:transaction:view', 'fleet:transaction:view'],
  '/api/transactions/statistics': ['transaction:view', 'transaction:customer:view', 'transaction:fleet:view'],
  '/api/transactions/customer-statistics': ['transaction:customer:view'],
  '/api/transactions/fleet-statistics': ['transaction:fleet:view'],
  '/api/transactions/customer-finance-summary': ['transaction:customer:view'],
  '/api/transactions/supplier-finance-summary': ['transaction:view'],
  '/api/transactions/fleet-finance-summary': ['transaction:fleet:view'],
  '/api/transactions/save': ['transaction:create', 'customer:transaction:create', 'supplier:transaction:create', 'fleet:transaction:create'],
  '/api/transactions/update': ['transaction:edit', 'customer:transaction:edit', 'supplier:transaction:edit', 'fleet:transaction:edit'],
  '/api/transactions/delete': ['transaction:delete', 'customer:transaction:delete', 'supplier:transaction:delete', 'fleet:transaction:delete'],
  '/api/transactions/export': ['transaction:export'],
  
  // 银行卡管理API
  '/api/finance/bank-cards/list': ['bank_card:view'],
  '/api/finance/bank-cards/save': ['bank_card:create'],
  '/api/finance/bank-cards/update': ['bank_card:edit'],
  '/api/finance/bank-cards/delete': ['bank_card:delete'],
  '/api/finance/bank-cards/detail': ['bank_card:view'],
  '/api/finance/bank-cards/options': ['*'],
  
  // 银行卡流水管理API
  '/api/finance/bank-card-flows/list': ['bank_card_flow:view'],
  '/api/finance/bank-card-flows/save': ['bank_card_flow:create'],
  '/api/finance/bank-card-flows/update': ['bank_card_flow:edit'],
  '/api/finance/bank-card-flows/delete': ['bank_card_flow:delete'],
  
  // 银行卡财务汇总API
  '/api/transactions/bank-card-finance-summary': ['transaction:view'],
  
  // 财务报表API
  '/api/finance/report/statistics': ['finance:report:view'],
  
  // 系统管理-用户API
  '/api/sys/users/list': ['user:view'],
  '/api/sys/users/save': ['user:create'],
  '/api/sys/users/update': ['user:edit'],
  '/api/sys/users/delete': ['user:delete'],
  '/api/sys/users/reset-password': ['user:reset-password'],
  
  // 系统管理-角色API
  '/api/sys/roles/list': ['role:view'],
  '/api/sys/roles/save': ['role:create'],
  '/api/sys/roles/update': ['role:edit'],
  '/api/sys/roles/delete': ['role:delete'],
  '/api/sys/roles/detail': ['role:view'],
  
  // 系统管理-权限API
  '/api/sys/permissions/list': ['role:view'], // 权限列表通常在角色管理中使用
  
  // 系统管理-日志API
  '/api/sys/logs/list': ['log:view'],
  '/api/sys/logs/export': ['log:export'],
  
  // 用户相关API
  '/api/user/change_password': ['user:change_password'],
};

// 免认证的API路径
const publicApiPaths = [
  '/api/auth/login',
  '/api/auth/login-mobile'
];

export default defineEventHandler(async (event) => {
  const url = event.node.req.url;
  
  // 如果是公共API，直接放行
  if (url && publicApiPaths.some(path => url.startsWith(path))) {
    return;
  }
  
  // 检查是否有需要权限的API
  const needsPermissionCheck = url && Object.keys(apiPermissionMap).some(
    apiPath => url.startsWith(apiPath)
  );
  
  // 如果不需要权限检查，直接放行
  if (!needsPermissionCheck) {
    return;
  }
  
  // 验证认证头
  const authHeader = getHeader(event, 'Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw createError({
      statusCode: 401,
      message: 'Unauthorized: Missing or malformed token',
    });
  }
  
  const token = authHeader.substring(7);
  const decodedPayload = verifyToken(token);
  
  if (!decodedPayload) {
    throw createError({
      statusCode: 401,
      message: 'Unauthorized: Invalid or expired token',
    });
  }
  
  // 直接从JWT获取权限信息，无需查询数据库
  const userPermissions = decodedPayload.permissions || [];
  
  // 检查是否有访问当前API的权限
  const requiredPermissions = getRequiredPermission(url);
  
  if (requiredPermissions && requiredPermissions.length > 0) {
    // 如果权限包含 '*'，表示所有用户都可以访问
    if (requiredPermissions.includes('*')) {
      // 允许访问，跳过权限检查
    } else {
      // 检查用户是否拥有任意一个所需权限
      const hasAnyPermission = requiredPermissions.some(permission => 
        userPermissions.includes(permission)
      );
      
      if (!hasAnyPermission) {
        throw createError({
          statusCode: 403,
          message: 'Forbidden: Insufficient permissions',
        });
      }
    }
  }
  
  // 将用户信息和权限附加到事件上下文中
  event.context.auth = {
    user: {
      id: decodedPayload.userId,
      name: decodedPayload.name,
      phone: decodedPayload.phone,
      roleId: decodedPayload.roleId,
      roleName: decodedPayload.roleName,
      permissions: userPermissions,
    },
  };
});

// 根据API路径获取所需权限
function getRequiredPermission(url: string | null): string[] | null {
  if (!url) {
    return null;
  }
  
  // 去掉查询参数
  const urlPath = url.split('?')[0];
  
  // 精确匹配
  if (urlPath in apiPermissionMap) {
    const permission = apiPermissionMap[urlPath];
    return Array.isArray(permission) ? permission : [permission];
  }
  
  // 前缀匹配
  for (const [apiPath, permission] of Object.entries(apiPermissionMap)) {
    if (urlPath.startsWith(apiPath)) {
      return Array.isArray(permission) ? permission : [permission];
    }
  }
  
  return null;
}