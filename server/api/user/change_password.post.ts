import { z } from "zod";
import type { AuthContext } from "~/server/types/auth";
import db from "~/server/db";
import { eq } from "drizzle-orm";
import { users } from "~/server/db/schema";
import { verifyPassword, hashPassword } from "~/server/utils/password.util";
import { createOperationLog } from "~/server/utils/logger";

const changePasswordSchema = z.object({
  phone: z.string().optional(), //可不传
  oldPassword: z.string().min(6),
  newPassword: z.string().min(6),
  confirmPassword: z.string().min(6),
});

export default defineEventHandler(async (event) => {
  const body = await readValidatedBody(event, (body) =>
    changePasswordSchema.safeParse(body)
  );
  if (!body.success) {
    return {
      code: -1,
      message: "请输入正确的密码",
    };
  }

  const { oldPassword, newPassword, confirmPassword, phone } = body.data;

  let user = null;
  if (phone) {
    user = await db.query.users.findFirst({
      where: eq(users.phone, phone),
    });
  } else {
    const auth = event.context.auth as AuthContext;
    if (!auth?.user) {
      return {
        code: -1,
        message: "请先登录",
      };
    }

    user = await db.query.users.findFirst({
      where: eq(users.id, Number(auth.user.id)),
    });
  }

  if (!user) {
    return {
      code: -1,
      message: "用户不存在",
    };
  }
  const isValidatePassword = await verifyPassword(oldPassword, user.password);
  if (!isValidatePassword) {
    return {
      code: -1,
      message: "原密码错误",
    };
  }
  const hashedPassword = await hashPassword(newPassword);
  await db
    .update(users)
    .set({ password: hashedPassword })
    .where(eq(users.id, Number(user.id)));

  // 记录操作日志
  await createOperationLog(event, {
    module: "user",
    action: "change_password",
    targetId: user.id.toString(),
    targetType: "user",
    oldData: { username: user.username },
    newData: { action: "修改密码" },
  });

  return {
    code: 0,
    message: "密码修改成功",
  };
});
