import { lands } from '~/server/db/schema';
import db from '~/server/db';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    // 获取所有未租状态的地块
    const result = await db
      .select({
        id: lands.id,
        name: lands.name,
        area: lands.area
      })
      .from(lands)
      .where(eq(lands.status, 2)); // 2 = 未租

    return {
      code: 0,
      message: '获取可用地块列表成功',
      data: result
    };
  } catch (error: any) {
    console.error('获取可用地块列表失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '获取可用地块列表失败',
      data: []
    };
  }
}); 