import { contracts, contractLands, lands, tenants } from '~/server/db/schema';
import db from '~/server/db';
import { eq, inArray } from 'drizzle-orm';
import dayjs from 'dayjs';
import { nanoid } from 'nanoid';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { 
      contractNumber, 
      contractStartDate, 
      contractEndDate, 
      contractAmount, 
      contractStatus = 1,
      tenantId,
      landIds = []
    } = body;

    // 验证必填字段
    if (!contractNumber || !contractStartDate || !contractEndDate || !contractAmount || !tenantId) {
      return {
        code: 400,
        message: '合同编号、开始日期、结束日期、合同金额和租户不能为空',
        data: {}
      };
    }

    // 验证合同编号是否已存在
    const existingContract = await db.select().from(contracts).where(eq(contracts.contractNumber, contractNumber));
    if (existingContract.length > 0) {
      return {
        code: 400,
        message: '合同编号已存在',
        data: {}
      };
    }

    // 验证租户是否存在
    const tenant = await db.select().from(tenants).where(eq(tenants.id, tenantId));
    if (tenant.length === 0) {
      return {
        code: 400,
        message: '选择的租户不存在',
        data: {}
      };
    }

    // 验证地块是否存在且状态为未租
    if (landIds.length > 0) {
      const selectedLands = await db.select().from(lands).where(inArray(lands.id, landIds));
      
      if (selectedLands.length !== landIds.length) {
        return {
          code: 400,
          message: '选择的地块中包含不存在的地块',
          data: {}
        };
      }

      const rentedLands = selectedLands.filter(land => land.status === 1);
      if (rentedLands.length > 0) {
        return {
          code: 400,
          message: `地块 ${rentedLands.map(land => land.name).join(', ')} 已被租用，无法关联`,
          data: {}
        };
      }
    }

    // 生成合同ID
    const contractId = nanoid(10);

    // 使用事务创建合同和关联地块
    await db.transaction(async (tx) => {
      // 创建合同
      await tx.insert(contracts).values({
        id: contractId,
        contractNumber,
        contractStartDate: new Date(contractStartDate),
        contractEndDate: new Date(contractEndDate),
        contractAmount: contractAmount.toString(),
        contractStatus,
        tenantId,
        createdAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
      });

      // 如果有地块需要关联
      if (landIds.length > 0) {
        // 创建合同地块关联
        const contractLandValues = landIds.map((landId: string) => ({
          contractId: contractId,
          landId,
          tenantId,
          createdAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
        }));

        await tx.insert(contractLands).values(contractLandValues);

        // 更新地块状态为已租
        await tx.update(lands)
          .set({ 
            status: 1,
            updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
          })
          .where(inArray(lands.id, landIds));
      }
    });

    return {
      code: 0,
      message: '合同创建成功',
      data: {}
    };
  } catch (error: any) {
    console.error('创建合同失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '创建合同失败',
      data: {}
    };
  }
}); 