import { contracts, contractLands, lands, tenants } from '~/server/db/schema';
import db from '~/server/db';
import { eq, and, ne, inArray } from 'drizzle-orm';
import dayjs from 'dayjs';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { 
      id,
      contractNumber, 
      contractStartDate, 
      contractEndDate, 
      contractAmount, 
      contractStatus,
      tenantId,
      landIds = []
    } = body;

    // 验证必填字段
    if (!id || !contractNumber || !contractStartDate || !contractEndDate || !contractAmount || !tenantId) {
      return {
        code: 400,
        message: '合同ID、合同编号、开始日期、结束日期、合同金额和租户不能为空',
        data: {}
      };
    }

    // 检查合同是否存在
    const existingContract = await db.select().from(contracts).where(eq(contracts.id, id));
    if (existingContract.length === 0) {
      return {
        code: 404,
        message: '合同不存在',
        data: {}
      };
    }

    // 验证合同编号是否已被其他合同使用
    const duplicateContract = await db.select().from(contracts).where(
      and(eq(contracts.contractNumber, contractNumber), ne(contracts.id, id))
    );
    if (duplicateContract.length > 0) {
      return {
        code: 400,
        message: '合同编号已被其他合同使用',
        data: {}
      };
    }

    // 验证租户是否存在
    const tenant = await db.select().from(tenants).where(eq(tenants.id, tenantId));
    if (tenant.length === 0) {
      return {
        code: 400,
        message: '选择的租户不存在',
        data: {}
      };
    }

    // 获取当前合同关联的地块
    const currentContractLands = await db.select().from(contractLands).where(eq(contractLands.contractId, id));
    const currentLandIds = currentContractLands.map(cl => cl.landId);

    // 验证新选择的地块是否存在且状态为未租（排除当前合同已关联的地块）
    if (landIds.length > 0) {
      const selectedLands = await db.select().from(lands).where(inArray(lands.id, landIds));
      
      if (selectedLands.length !== landIds.length) {
        return {
          code: 400,
          message: '选择的地块中包含不存在的地块',
          data: {}
        };
      }

      // 检查新增的地块（不在当前合同中的地块）是否已被租用
      const newLandIds = landIds.filter((landId: string) => !currentLandIds.includes(landId));
      if (newLandIds.length > 0) {
        const newLands = selectedLands.filter(land => newLandIds.includes(land.id));
        const rentedNewLands = newLands.filter(land => land.status === 1);
        if (rentedNewLands.length > 0) {
          return {
            code: 400,
            message: `地块 ${rentedNewLands.map(land => land.name).join(', ')} 已被租用，无法关联`,
            data: {}
          };
        }
      }
    }

    // 使用事务更新合同和关联地块
    await db.transaction(async (tx) => {
      // 更新合同
      await tx.update(contracts).set({
        contractNumber,
        contractStartDate: new Date(contractStartDate),
        contractEndDate: new Date(contractEndDate),
        contractAmount: contractAmount.toString(),
        contractStatus,
        tenantId,
        updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
      }).where(eq(contracts.id, id));

      // 处理地块关联变更
      // 如果合同状态为已过期（状态值为2），则保持现有地块关联不变
      if (contractStatus === 2) {
        // 合同过期时，不删除地块关联，也不修改地块状态
        // 只更新合同本身的状态，保持地块关联关系
        console.log('合同已过期，保持现有地块关联关系不变');
        // 将之前关联的地块状态改为未租
        if (currentLandIds.length > 0) {
          await tx.update(lands)
            .set({ 
              status: 2,
              updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
            })
            .where(inArray(lands.id, currentLandIds));
        }
      } else {
        // 正常更新地块关联（非过期状态）
        // 删除所有当前合同的地块关联
        await tx.delete(contractLands).where(eq(contractLands.contractId, id));

        // 将之前关联的地块状态改为未租
        if (currentLandIds.length > 0) {
          await tx.update(lands)
            .set({ 
              status: 2,
              updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
            })
            .where(inArray(lands.id, currentLandIds));
        }

        // 如果有新的地块需要关联
        if (landIds.length > 0) {
          // 创建新的合同地块关联
          const contractLandValues = landIds.map((landId: string) => ({
            contractId: id,
            landId,
            tenantId,
            createdAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
          }));

          await tx.insert(contractLands).values(contractLandValues);

          // 更新新关联的地块状态为已租
          await tx.update(lands)
            .set({ 
              status: 1,
              updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
            })
            .where(inArray(lands.id, landIds));
        }
      }
    });

    return {
      code: 0,
      message: '合同更新成功',
      data: {}
    };
  } catch (error: any) {
    console.error('更新合同失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '更新合同失败',
      data: {}
    };
  }
}); 