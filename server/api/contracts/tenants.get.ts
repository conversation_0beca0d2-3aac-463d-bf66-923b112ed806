import { tenants } from '~/server/db/schema';
import db from '~/server/db';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    // 获取所有正常状态的租户
    const result = await db
      .select({
        id: tenants.id,
        name: tenants.name,
        phone: tenants.phone
      })
      .from(tenants)
      .where(eq(tenants.status, 1));

    return {
      code: 0,
      message: '获取租户列表成功',
      data: result
    };
  } catch (error: any) {
    console.error('获取租户列表失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '获取租户列表失败',
      data: []
    };
  }
}); 