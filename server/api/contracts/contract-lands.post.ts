import { contractLands, lands } from '~/server/db/schema';
import db from '~/server/db';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { contractId } = body;

    if (!contractId) {
      return {
        code: 400,
        message: '合同ID不能为空',
        data: []
      };
    }

    // 获取合同关联的地块
    const result = await db
      .select({
        landId: contractLands.landId,
        landName: lands.name,
        landArea: lands.area
      })
      .from(contractLands)
      .leftJoin(lands, eq(contractLands.landId, lands.id))
      .where(eq(contractLands.contractId, contractId));

    return {
      code: 0,
      message: '获取合同关联地块成功',
      data: result
    };
  } catch (error: any) {
    console.error('获取合同关联地块失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '获取合同关联地块失败',
      data: []
    };
  }
}); 