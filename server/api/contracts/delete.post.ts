import { contracts, contractLands, lands } from '~/server/db/schema';
import db from '~/server/db';
import { eq, inArray } from 'drizzle-orm';
import dayjs from 'dayjs';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id } = body;

    // 验证必填字段
    if (!id) {
      return {
        code: 400,
        message: '合同ID不能为空',
        data: {}
      };
    }

    // 检查合同是否存在
    const existingContract = await db.select().from(contracts).where(eq(contracts.id, id));
    if (existingContract.length === 0) {
      return {
        code: 404,
        message: '合同不存在',
        data: {}
      };
    }

    // 获取合同关联的地块
    const contractLandsList = await db.select().from(contractLands).where(eq(contractLands.contractId, id));
    const landIds = contractLandsList.map(cl => cl.landId);

    // 使用事务删除合同和关联数据
    await db.transaction(async (tx) => {
      // 删除合同地块关联
      await tx.delete(contractLands).where(eq(contractLands.contractId, id));

      // 将关联的地块状态改为未租
      if (landIds.length > 0) {
        await tx.update(lands)
          .set({ 
            status: 2,
            updatedAt: dayjs().format('YYYY-MM-DD HH:mm')
          })
          .where(inArray(lands.id, landIds));
      }

      // 删除合同
      await tx.delete(contracts).where(eq(contracts.id, id));
    });

    return {
      code: 0,
      message: '合同删除成功',
      data: {}
    };
  } catch (error: any) {
    console.error('删除合同失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '删除合同失败',
      data: {}
    };
  }
}); 