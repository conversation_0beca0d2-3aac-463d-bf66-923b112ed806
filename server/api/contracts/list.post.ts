import { desc, like, and, eq } from 'drizzle-orm';
import { contracts, tenants, contractLands, lands } from '~/server/db/schema';
import db from '~/server/db';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { pageSize = 10, current = 1, keyword } = body;

    // 构建查询条件
    const conditions = [];
    if (keyword) {
      conditions.push(like(contracts.contractNumber, `%${keyword}%`));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // 查询总数
    const totalResult = await db.select().from(contracts).where(whereClause);
    const total = totalResult.length;

    // 查询分页数据
    const offset = (current - 1) * pageSize;
    const result = await db
      .select({
        id: contracts.id,
        contractNumber: contracts.contractNumber,
        contractStartDate: contracts.contractStartDate,
        contractEndDate: contracts.contractEndDate,
        contractAmount: contracts.contractAmount,
        contractStatus: contracts.contractStatus,
        tenantId: contracts.tenantId,
        tenantName: tenants.name,
        createdAt: contracts.createdAt,
        updatedAt: contracts.updatedAt
      })
      .from(contracts)
      .leftJoin(tenants, eq(contracts.tenantId, tenants.id))
      .where(whereClause)
      .orderBy(desc(contracts.createdAt))
      .limit(pageSize)
      .offset(offset);

    // 为每个合同获取关联的地块信息
    const contractsWithLands = await Promise.all(
      result.map(async (contract) => {
        const contractLandsList = await db
          .select({
            landId: contractLands.landId,
            landName: lands.name,
            landArea: lands.area
          })
          .from(contractLands)
          .leftJoin(lands, eq(contractLands.landId, lands.id))
          .where(eq(contractLands.contractId, contract.id));

        return {
          ...contract,
          lands: contractLandsList
        };
      })
    );

    return {
      code: 0,
      message: '获取合同列表成功',
      data: {
        list: contractsWithLands,
        total,
        pageSize,
        current
      }
    };
  } catch (error: any) {
    console.error('获取合同列表失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '获取合同列表失败',
      data: {}
    };
  }
}); 