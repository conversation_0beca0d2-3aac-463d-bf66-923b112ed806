import { desc, like, and, eq, gte, lte } from 'drizzle-orm';
import { orders, tenants, crops, lands } from '~/server/db/schema';
import db from '~/server/db';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { 
      pageSize = 10, 
      current = 1, 
      keyword,
      tenantId,
      orderStatus,
      startDate,
      endDate
    } = body;

    // 构建查询条件
    const conditions = [];
    
    if (keyword) {
      conditions.push(like(orders.orderNumber, `%${keyword}%`));
    }
    
    if (tenantId) {
      conditions.push(eq(orders.tenantId, tenantId));
    }
    
    if (orderStatus !== undefined && orderStatus !== '') {
      conditions.push(eq(orders.orderStatus, orderStatus));
    }
    
    if (startDate) {
      conditions.push(gte(orders.orderDate, new Date(startDate)));
    }
    
    if (endDate) {
      conditions.push(lte(orders.orderDate, new Date(endDate)));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // 查询总数
    const totalResult = await db.select().from(orders).where(whereClause);
    const total = totalResult.length;

    // 查询分页数据
    const offset = (current - 1) * pageSize;
    const result = await db
      .select({
        id: orders.id,
        orderNumber: orders.orderNumber,
        orderDate: orders.orderDate,
        tenantId: orders.tenantId,
        tenantName: tenants.name,
        cropId: orders.cropId,
        cropName: crops.name,
        landId: orders.landId,
        landName: lands.name,
        receiverName: orders.receiverName,
        receiverPhone: orders.receiverPhone,
        receiverAddress: orders.receiverAddress,
        logisticsCompany: orders.logisticsCompany,
        logisticsNumber: orders.logisticsNumber,
        orderStatus: orders.orderStatus,
        createdAt: orders.createdAt,
        updatedAt: orders.updatedAt
      })
      .from(orders)
      .leftJoin(tenants, eq(orders.tenantId, tenants.id))
      .leftJoin(crops, eq(orders.cropId, crops.id))
      .leftJoin(lands, eq(orders.landId, lands.id))
      .where(whereClause)
      .orderBy(desc(orders.orderDate))
      .limit(pageSize)
      .offset(offset);

    return {
      code: 0,
      message: '获取订单列表成功',
      data: {
        list: result,
        total,
        pageSize,
        current
      }
    };
  } catch (error: any) {
    console.error('获取订单列表失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '获取订单列表失败',
      data: {}
    };
  }
}); 