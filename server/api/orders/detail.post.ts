import { eq } from 'drizzle-orm';
import { orders, tenants, crops, lands } from '~/server/db/schema';
import db from '~/server/db';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id } = body;

    if (!id) {
      return {
        code: 400,
        message: '订单ID不能为空',
        data: {}
      };
    }

    // 查询订单详情
    const result = await db
      .select({
        id: orders.id,
        orderNumber: orders.orderNumber,
        orderDate: orders.orderDate,
        tenantId: orders.tenantId,
        tenantName: tenants.name,
        tenantPhone: tenants.phone,
        cropId: orders.cropId,
        cropName: crops.name,
        cropPlantingArea: crops.plantingArea,
        landId: orders.landId,
        landName: lands.name,
        landArea: lands.area,
        receiverName: orders.receiverName,
        receiverPhone: orders.receiverPhone,
        receiverAddress: orders.receiverAddress,
        logisticsCompany: orders.logisticsCompany,
        logisticsNumber: orders.logisticsNumber,
        orderStatus: orders.orderStatus,
        createdAt: orders.createdAt,
        updatedAt: orders.updatedAt
      })
      .from(orders)
      .leftJoin(tenants, eq(orders.tenantId, tenants.id))
      .leftJoin(crops, eq(orders.cropId, crops.id))
      .leftJoin(lands, eq(orders.landId, lands.id))
      .where(eq(orders.id, id));

    if (result.length === 0) {
      return {
        code: 404,
        message: '订单不存在',
        data: {}
      };
    }

    return {
      code: 0,
      message: '获取订单详情成功',
      data: result[0]
    };
  } catch (error: any) {
    console.error('获取订单详情失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '获取订单详情失败',
      data: {}
    };
  }
}); 