import { eq } from 'drizzle-orm';
import { orders } from '~/server/db/schema';
import db from '~/server/db';
import dayjs from 'dayjs';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id, logisticsCompany, logisticsNumber } = body;

    // 验证必填字段
    if (!id || !logisticsCompany || !logisticsNumber) {
      return {
        code: 400,
        message: '订单ID、物流公司和快递单号不能为空',
        data: {}
      };
    }

    // 检查订单是否存在
    const existingOrder = await db.select().from(orders).where(eq(orders.id, id));
    if (existingOrder.length === 0) {
      return {
        code: 404,
        message: '订单不存在',
        data: {}
      };
    }

    // 检查订单状态是否为待处理
    if (existingOrder[0].orderStatus !== 1) {
      const statusText = {
        2: '已发货',
        3: '已收货',
        4: '已取消',
        5: '已取消'
      }[existingOrder[0].orderStatus] || '未知状态';
      
      return {
        code: 400,
        message: `订单状态为${statusText}，无法发货`,
        data: {}
      };
    }

    // 更新订单状态为已发货
    await db.update(orders).set({
      logisticsCompany,
      logisticsNumber,
      orderStatus: 2, // 已发货
      updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
    }).where(eq(orders.id, id));

    return {
      code: 0,
      message: '订单发货成功',
      data: {}
    };
  } catch (error: any) {
    console.error('订单发货失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '订单发货失败',
      data: {}
    };
  }
}); 