import { lands } from '~/server/db/schema';
import db from '~/server/db';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id } = body;

    // 验证必填字段
    if (!id) {
      return {
        code: 400,
        message: '地块ID不能为空',
        data: {}
      };
    }

    // 检查地块是否存在
    const existingLand = await db.select().from(lands).where(eq(lands.id, id));
    if (existingLand.length === 0) {
      return {
        code: 404,
        message: '地块不存在',
        data: {}
      };
    }

    // 删除地块
    await db.delete(lands).where(eq(lands.id, id));

    return {
      code: 0,
      message: '地块删除成功',
      data: {}
    };
  } catch (error: any) {
    console.error('删除地块失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '删除地块失败',
      data: {}
    };
  }
}); 