import { lands, contractLands, tenants, crops } from '~/server/db/schema';
import db from '~/server/db';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    // 查询所有有租户的地块（已租用的地块）
    const availableLands = await db
      .select({
        landId: lands.id,
        landName: lands.name,
        landArea: lands.area,
        tenantId: contractLands.tenantId,
        tenantName: tenants.name,
        tenantPhone: tenants.phone,
      })
      .from(lands)
      .innerJoin(contractLands, eq(lands.id, contractLands.landId))
      .innerJoin(tenants, eq(contractLands.tenantId, tenants.id))
      .where(eq(lands.status, 1)); // 只查询已租用的地块

    // 为每个地块查询相关的作物
    const landsWithCrops = await Promise.all(
      availableLands.map(async (land) => {
        const landCrops = await db
          .select({
            id: crops.id,
            name: crops.name,
            emoji: crops.emoji,
            status: crops.status,
          })
          .from(crops)
          .where(eq(crops.landId, land.landId));

        return {
          ...land,
          crops: landCrops
        };
      })
    );

    return {
      code: 0,
      message: '获取成功',
      data: {
        lands: landsWithCrops
      }
    };
  } catch (error: any) {
    console.error('获取可用地块失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '获取可用地块失败',
      data: {}
    };
  }
}); 