import { lands } from '~/server/db/schema';
import db from '~/server/db';
import { eq } from 'drizzle-orm';
import dayjs from 'dayjs';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { name, area, status = 1 } = body;

    // 验证必填字段
    if (!name || !area) {
      return {
        code: 400,
        message: '地块名称和面积不能为空',
        data: {}
      };
    }

    // 检查地块名称是否已存在
    const existingLand = await db.select().from(lands).where(eq(lands.name, name));
    if (existingLand.length > 0) {
      return {
        code: 400,
        message: '地块名称已存在',
        data: {}
      };
    }

    // 创建地块
    await db.insert(lands).values({
      name,
      area: area.toString(),
      status,
      createdAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
    });

    return {
      code: 0,
      message: '地块创建成功',
      data: {}
    };
  } catch (error: any) {
    console.error('创建地块失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '创建地块失败',
      data: {}
    };
  }
}); 