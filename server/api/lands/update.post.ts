import { lands } from '~/server/db/schema';
import db from '~/server/db';
import { eq, and, ne } from 'drizzle-orm';
import dayjs from 'dayjs';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id, name, area, status } = body;
    // 验证必填字段
    if (!id || !name || !area) {
      return {
        code: 400,
        message: '地块ID、名称和面积不能为空',
        data: {}
      };
    }

    // 检查地块是否存在
    const existingLand = await db.select().from(lands).where(eq(lands.id, id));
    if (existingLand.length === 0) {
      return {
        code: 404,
        message: '地块不存在',
        data: {}
      };
    }

    // 检查地块名称是否已被其他地块使用
    const duplicateName = await db.select().from(lands).where(
      and(eq(lands.name, name), ne(lands.id, id))
    );
    if (duplicateName.length > 0) {
      return {
        code: 400,
        message: '地块名称已存在',
        data: {}
      };
    }

    // 更新地块
    await db.update(lands).set({
      name,
      area: area.toString(),
      status,
      updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
    }).where(eq(lands.id, id));

    return {
      code: 0,
      message: '地块更新成功',
      data: {}
    };
  } catch (error: any) {
    console.error('更新地块失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '更新地块失败',
      data: {}
    };
  }
}); 