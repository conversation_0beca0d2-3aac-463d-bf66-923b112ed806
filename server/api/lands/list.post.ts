import { desc, like, and } from 'drizzle-orm';
import { lands, users } from '~/server/db/schema';
import db from '~/server/db';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { pageSize = 10, current = 1, keyword } = body;
    
    // 构建查询条件
    const conditions = [];
    if (keyword) {
      conditions.push(like(lands.name, `%${keyword}%`));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // 查询总数
    const totalResult = await db.select().from(lands).where(whereClause);
    const total = totalResult.length;

    // 查询分页数据
    const offset = (current - 1) * pageSize;
    const result = await db
      .select({
        id: lands.id,
        name: lands.name,
        area: lands.area,
        status: lands.status,
        createdAt: lands.createdAt,
        updatedAt: lands.updatedAt
      })
      .from(lands)
      .where(whereClause)
      .orderBy(desc(lands.createdAt))
      .limit(pageSize)
      .offset(offset);

    return {
      code: 0,
      message: '获取地块列表成功',
      data: {
        list: result,
        total,
        pageSize,
        current
      }
    };
  } catch (error: any) {
    console.error('获取地块列表失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '获取地块列表失败',
      data: {}
    };
  }
}); 