import { z } from "zod";
import db from "~/server/db";
import { roles, rolePermissions } from "~/server/db/schema";
import { sql } from "drizzle-orm";
import { createOperationLog } from "~/server/utils/logger";

const saveRoleSchema = z.object({
  name: z.string().min(1, "角色名称不能为空"),
  permissionIds: z.array(z.number()).min(1, "请至少选择一个权限")
});

export default defineEventHandler(async (event) => {
  try {
    const body = await readValidatedBody(event, (body) =>
      saveRoleSchema.safeParse(body)
    );

    if (!body.success) {
      return {
        code: -1,
        message: "请检查输入数据",
      };
    }

    const { name, permissionIds } = body.data;

    let roleId: number;

    // 开启事务
    await db.transaction(async (tx) => {
      // 创建角色并获取插入的ID
      const [insertedRole] = await tx.execute(
        sql`INSERT INTO roles (name) VALUES (${name})`
      );
      
      roleId = insertedRole.insertId as number;

      // 创建角色权限关联
      if (permissionIds.length > 0) {
        // 构建批量插入的值
        const values = permissionIds.map(id => sql`(${roleId}, ${id})`);
        await tx.execute(sql`
          INSERT INTO role_permissions (role_id, permission_id) 
          VALUES ${sql.join(values, sql`, `)}
        `);
      }
    });

    // 记录操作日志
    await createOperationLog(event, {
      module: 'role',
      action: 'create',
      targetId: roleId!.toString(),
      targetType: 'role',
      newData: { name, permissionIds },
    });

    return {
      code: 0,
      message: "角色创建成功"
    };
  } catch (error) {
    console.error('Error saving role:', error);
    return {
      code: -1,
      message: "角色创建失败"
    };
  }
}); 