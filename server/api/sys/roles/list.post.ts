import db from "~/server/db";
import { roles, rolePermissions, permissions } from "~/server/db/schema";

export default defineEventHandler(async (event) => {
  try {
    // 获取所有角色及其权限
    const rolesWithPermissions = await db.query.roles.findMany({
      with: {
        rolePermissions: {
          with: {
            permission: true
          }
        }
      }
    });

    // 处理数据格式
    const formattedRoles = rolesWithPermissions.map(role => ({
      id: role.id,
      name: role.name,
      permissions: role.rolePermissions.map(rp => rp.permission?.name || '').filter(Boolean)
    }));

    return {
      code: 0,
      data: formattedRoles,
      message: "success"
    };
  } catch (error) {
    console.error('Error fetching roles:', error);
    return {
      code: -1,
      message: "获取角色列表失败"
    };
  }
});