import { z } from "zod";
import db from "~/server/db";
import { roles, rolePermissions } from "~/server/db/schema";
import { eq } from "drizzle-orm";

const detailSchema = z.object({
  id: z.number().min(1, "角色ID不能为空"),
});

export default defineEventHandler(async (event) => {
  try {
    const body = await readValidatedBody(event, (body) =>
      detailSchema.safeParse(body)
    );

    if (!body.success) {
      return {
        code: -1,
        message: "请检查输入数据",
      };
    }

    const { id } = body.data;

    // 获取角色信息及其权限
    const roleWithPermissions = await db.query.roles.findFirst({
      where: eq(roles.id, id),
      with: {
        rolePermissions: {
          with: {
            permission: true
          }
        }
      }
    });

    if (!roleWithPermissions) {
      return {
        code: -1,
        message: "角色不存在"
      };
    }

    // 提取权限ID列表
    const permissionIds = roleWithPermissions.rolePermissions.map(rp => rp.permissionId);

    return {
      code: 0,
      data: {
        id: roleWithPermissions.id,
        name: roleWithPermissions.name,
        description: roleWithPermissions.description,
        permissionIds
      },
      message: "success"
    };
  } catch (error) {
    console.error('Error fetching role detail:', error);
    return {
      code: -1,
      message: "获取角色详情失败"
    };
  }
}); 