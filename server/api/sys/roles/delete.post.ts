import { z } from "zod";
import db from "~/server/db";
import { roles, rolePermissions, users } from "~/server/db/schema";
import { eq, and } from "drizzle-orm";
import { createOperationLog } from "~/server/utils/logger";

const deleteRoleSchema = z.object({
  id: z.number().min(1, "角色ID不能为空"),
});

export default defineEventHandler(async (event) => {
  try {
    const body = await readValidatedBody(event, (body) =>
      deleteRoleSchema.safeParse(body)
    );

    if (!body.success) {
      return {
        code: -1,
        message: "请检查输入数据",
      };
    }

    const { id } = body.data;

    // 检查角色是否存在
    const existingRole = await db.query.roles.findFirst({
      where: eq(roles.id, id)
    });

    if (!existingRole) {
      return {
        code: -1,
        message: "角色不存在"
      };
    }

    // 检查是否有用户使用该角色
    const usersWithRole = await db.query.users.findFirst({
      where: eq(users.roleId, id)
    });

    if (usersWithRole) {
      return {
        code: -1,
        message: "该角色下存在用户，无法删除"
      };
    }

    // 开启事务删除角色及其权限关联
    await db.transaction(async (tx) => {
      // 先删除角色权限关联
      await tx.delete(rolePermissions)
        .where(eq(rolePermissions.roleId, id));

      // 再删除角色
      await tx.delete(roles)
        .where(eq(roles.id, id));
    });

    // 记录操作日志
    await createOperationLog(event, {
      module: 'role',
      action: 'delete',
      targetId: id.toString(),
      targetType: 'role',
      oldData: existingRole,
    });

    return {
      code: 0,
      message: "角色删除成功"
    };
  } catch (error) {
    console.error('Error deleting role:', error);
    return {
      code: -1,
      message: "角色删除失败"
    };
  }
}); 