import { z } from "zod";
import db from "~/server/db";
import { roles, rolePermissions } from "~/server/db/schema";
import { sql, eq } from "drizzle-orm";
import { createOperationLog } from "~/server/utils/logger";

const updateRoleSchema = z.object({
  id: z.number().min(1, "角色ID不能为空"),
  name: z.string().min(1, "角色名称不能为空"),
  permissionIds: z.array(z.number()).min(1, "请至少选择一个权限")
});

export default defineEventHandler(async (event) => {
  try {
    const body = await readValidatedBody(event, (body) =>
      updateRoleSchema.safeParse(body)
    );

    if (!body.success) {
      return {
        code: -1,
        message: "请检查输入数据",
      };
    }

    const { id, name, permissionIds } = body.data;

    // 检查角色是否存在
    const existingRole = await db.query.roles.findFirst({
      where: eq(roles.id, id)
    });

    if (!existingRole) {
      return {
        code: -1,
        message: "角色不存在"
      };
    }

    // 开启事务
    await db.transaction(async (tx) => {
      // 更新角色名称
      await tx.update(roles)
        .set({ name })
        .where(eq(roles.id, id));

      // 删除旧的权限关联
      await tx.delete(rolePermissions)
        .where(eq(rolePermissions.roleId, id));

      // 创建新的权限关联
      if (permissionIds.length > 0) {
        const values = permissionIds.map(permId => sql`(${id}, ${permId})`);
        await tx.execute(sql`
          INSERT INTO role_permissions (role_id, permission_id) 
          VALUES ${sql.join(values, sql`, `)}
        `);
      }
    });

    // 记录操作日志
    await createOperationLog(event, {
      module: 'role',
      action: 'update',
      targetId: id.toString(),
      targetType: 'role',
      oldData: existingRole,
      newData: { name, permissionIds },
    });

    return {
      code: 0,
      message: "角色更新成功"
    };
  } catch (error) {
    console.error('Error updating role:', error);
    return {
      code: -1,
      message: "角色更新失败"
    };
  }
}); 