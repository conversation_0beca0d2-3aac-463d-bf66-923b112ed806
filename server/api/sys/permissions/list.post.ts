import db from "~/server/db";

interface PermissionItem {
  id: number;
  name: string;
  code: string;
  group: string;
  type: string;
  parentId: number | null;
  sort: number;
  description: string | null;
}

interface TreePermission extends PermissionItem {
  children?: TreePermission[] | null;
  buttonPermissions?: PermissionItem[] | null;
}

export default defineEventHandler(async (event) => {
  try {
    const permissionsList = await db.query.permissions.findMany({
      orderBy: (permissions, { asc }) => [asc(permissions.sort)]
    });

    // 构建树形结构的权限数据
    const buildPermissionTree = (): TreePermission[] => {
      // 先获取所有权限
      const allPermissions: PermissionItem[] = permissionsList.map(permission => ({
        id: permission.id,
        name: permission.name,
        code: permission.code,
        group: permission.group || '',
        type: permission.type || 'button',
        parentId: permission.parentId, // drizzle会自动处理字段名映射
        sort: permission.sort || 0,
        description: permission.description
      }));

      // 构建树形结构
      const buildTree = (parentId: number | null = null): TreePermission[] => {
        const menuItems = allPermissions
          .filter(p => p.parentId === parentId && p.type === 'menu')
          .sort((a, b) => a.sort - b.sort);
        
        return menuItems.map(menu => {
          const children: TreePermission[] = buildTree(menu.id);
          const buttonPermissions: PermissionItem[] = allPermissions
            .filter(p => p.parentId === menu.id && p.type === 'button')
            .sort((a, b) => a.sort - b.sort);
          
          return {
            ...menu,
            children: children.length > 0 ? children : null,
            buttonPermissions: buttonPermissions.length > 0 ? buttonPermissions : null
          };
        });
      };

      return buildTree();
    };

    const treeData = buildPermissionTree();

    // 同时返回按组分类的数据（兼容性）
    const groupedPermissions = permissionsList.reduce((acc, permission) => {
      const group = permission.group || '其他';
      if (!acc[group]) {
        acc[group] = [];
      }
      acc[group].push({
        id: permission.id,
        name: permission.name,
        code: permission.code,
        group: permission.group,
        type: permission.type,
        parentId: permission.parentId,
        sort: permission.sort,
        description: permission.description
      });
      return acc;
    }, {} as Record<string, any[]>);

    return {
      code: 0,
      data: {
        tree: treeData,
        grouped: groupedPermissions,
        list: permissionsList.map(permission => ({
          id: permission.id,
          name: permission.name,
          code: permission.code,
          group: permission.group,
          type: permission.type,
          parentId: permission.parentId,
          sort: permission.sort,
          description: permission.description
        }))
      },
      message: "success"
    };
  } catch (error) {
    console.error('Error fetching permissions:', error);
    return {
      code: -1,
      message: "获取权限列表失败"
    };
  }
}); 