import { z } from "zod";
import db from "~/server/db";
import { users } from "~/server/db/schema";
import { eq, sql } from "drizzle-orm";

// 定义请求参数验证schema
const listUsersSchema = z.object({
  pageSize: z.number().min(1).default(10),
  current: z.number().min(1).default(1),
});

export default defineEventHandler(async (event) => {
  try {
    // 验证请求参数
    const body = await readValidatedBody(event, (body) =>
      listUsersSchema.safeParse(body)
    );

    if (!body.success) {
      return {
        code: -1,
        message: "请检查分页参数",
        data: [],
        total: 0
      };
    }

    const { pageSize, current } = body.data;
    const offset = (current - 1) * pageSize;

    // 获取总记录数
    const totalResult = await db.select({ 
      count: sql<number>`count(*)` 
    }).from(users);
    const total = Number(totalResult[0].count);

    // 获取分页数据
    const usersList = await db.query.users.findMany({
      limit: pageSize,
      offset: offset,
      with: {
        role: true
      }
    });

    // 处理返回数据格式
    const formattedUsers = usersList.map(user => ({
      id: user.id,
      username: user.username,
      phone: user.phone,
      status: user.status,
      role: user.role ? {
        id: user.role.id,
        name: user.role.name
      } : null,
      createdAt: user.createdAt
    }));

    return {
      code: 0,
      data: {
        list: formattedUsers,
        total: total
      },
      message: "success"
    };
  } catch (error) {
    console.error('Error fetching users:', error);
    return {
      code: -1,
      data: [],
      total: 0,
      message: "获取用户列表失败"
    };
  }
}); 