import { z } from "zod";
import db from "~/server/db";
import { users } from "~/server/db/schema";
import { eq } from "drizzle-orm";
import { createOperationLog } from "~/server/utils/logger";

const deleteUserSchema = z.object({
  id: z.number().min(1, "用户ID不能为空"),
});

export default defineEventHandler(async (event) => {
  try {
    const body = await readValidatedBody(event, (body) =>
      deleteUserSchema.safeParse(body)
    );

    if (!body.success) {
      return {
        code: -1,
        message: "请检查输入数据",
      };
    }

    const { id } = body.data;

    // 检查用户是否存在
    const existingUser = await db.query.users.findFirst({
      where: eq(users.id, id)
    });

    if (!existingUser) {
      return {
        code: -1,
        message: "用户不存在"
      };
    }

    // 检查是否为管理员用户（id = 1）
    if (existingUser.id === 1) {
      return {
        code: -1,
        message: "不能删除初始管理员用户"
      };
    }

    // 删除用户
    await db.delete(users).where(eq(users.id, id));

    // 记录操作日志
    await createOperationLog(event, {
      module: 'user',
      action: 'delete',
      targetId: id.toString(),
      targetType: 'user',
      oldData: existingUser,
    });

    return {
      code: 0,
      message: "用户删除成功"
    };
  } catch (error) {
    console.error('Error deleting user:', error);
    return {
      code: -1,
      message: "用户删除失败"
    };
  }
}); 