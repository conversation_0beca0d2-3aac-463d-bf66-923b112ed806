import { z } from "zod";
import db from "~/server/db";
import { users } from "~/server/db/schema";
import { eq, and, not } from "drizzle-orm";
import { createOperationLog } from "~/server/utils/logger";

const updateUserSchema = z.object({
  id: z.number().min(1, "用户ID不能为空"),
  username: z.string().min(1, "用户名不能为空"),
  phone: z.string().min(1, "请输入正确的手机号码"),
  roleId: z.number().min(1, "请选择角色"),
  status: z.number().min(1, "请选择状态"),
});

export default defineEventHandler(async (event) => {
  try {
    const body = await readValidatedBody(event, (body) =>
      updateUserSchema.safeParse(body)
    );

    if (!body.success) {
      return {
        code: -1,
        message: "请检查输入数据",
      };
    }

    const { id, username, phone, roleId, status } = body.data;

    // 检查用户是否存在
    const existingUser = await db.query.users.findFirst({
      where: eq(users.id, id)
    });

    if (!existingUser) {
      return {
        code: -1,
        message: "用户不存在"
      };
    }

    // 检查手机号是否被其他用户使用
    const phoneExists = await db.query.users.findFirst({
      where: and(
        eq(users.phone, phone),
        not(eq(users.id, id))
      )
    });

    if (phoneExists) {
      return {
        code: -1,
        message: "手机号已被其他用户使用"
      };
    }

    // 更新用户信息
    await db.update(users)
      .set({
        username,
        phone,
        roleId,
        status
      })
      .where(eq(users.id, id));

    // 记录操作日志
    await createOperationLog(event, {
      module: 'user',
      action: 'update',
      targetId: id.toString(),
      targetType: 'user',
      oldData: existingUser,
      newData: { username, phone, roleId, status },
    });

    return {
      code: 0,
      message: "用户更新成功"
    };
  } catch (error) {
    console.error('Error updating user:', error);
    return {
      code: -1,
      message: "用户更新失败"
    };
  }
}); 