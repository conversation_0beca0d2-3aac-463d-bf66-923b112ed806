import { z } from "zod";
import db from "~/server/db";
import { users } from "~/server/db/schema";
import { eq } from "drizzle-orm";
import { hashPassword } from "~/server/utils/password.util";
import { createOperationLog } from "~/server/utils/logger";

const createUserSchema = z.object({
  username: z.string().min(1, "用户名不能为空"),
  phone: z.string().min(1, "请输入正确的手机号码"),
  password: z.string().min(6, "密码不能少于6位"),
  roleId: z.number().min(1, "请选择角色"),
});

export default defineEventHandler(async (event) => {
  try {
    const body = await readValidatedBody(event, (body) =>
      createUserSchema.safeParse(body)
    );

    if (!body.success) {
      return {
        code: -1,
        message: "请检查输入数据",
      };
    }

    const { username, phone, password, roleId } = body.data;

    // 检查手机号是否已存在
    const existingUser = await db.query.users.findFirst({
      where: eq(users.phone, phone)
    });

    if (existingUser) {
      return {
        code: -1,
        message: "手机号已存在"
      };
    }

    // 密码加密
    const hashedPassword = await hashPassword(password);

    // 创建用户
    const result = await db.insert(users).values({
      username,
      phone,
      password: hashedPassword,
      roleId
    }).$returningId();

    // 记录操作日志
    await createOperationLog(event, {
      module: 'user',
      action: 'create',
      targetId: result[0].id.toString(),
      targetType: 'user',
      newData: { username, phone, roleId },
    });

    return {
      code: 0,
      message: "用户创建成功"
    };
  } catch (error) {
    console.error('Error creating user:', error);
    return {
      code: -1,
      message: "用户创建失败"
    };
  }
}); 