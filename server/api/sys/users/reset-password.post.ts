import db from "~/server/db";
import { users } from '~/server/db/schema';
import { eq } from 'drizzle-orm';
import { hashPassword } from '~/server/utils/password.util';
import { z } from 'zod';
import { createOperationLog } from "~/server/utils/logger";
const resetPasswordSchema = z.object({
    id: z.number().min(1, "用户ID不能为空"),
    password: z.string().min(6, "密码不能少于6位"),
});

export default defineEventHandler(async (event) => {
    const body = await readValidatedBody(event, (body) => resetPasswordSchema.safeParse(body));
    if (!body.success) {
        return {
            code: -1,
            message: '请检查输入数据'
        }
    }
    
    const { id, password } = body.data;

    // 密码加密
    const hashedPassword = await hashPassword(password);

    try {
        // 获取用户信息
        const existingUser = await db.query.users.findFirst({
            where: eq(users.id, id)
        });

        if (!existingUser) {
            return {
                code: -1,
                message: '用户不存在'
            };
        }

        // 更新用户密码
        await db.update(users)
            .set({ 
                password: hashedPassword,
            })
            .where(eq(users.id, id));

        // 记录操作日志
        await createOperationLog(event, {
            module: 'user',
            action: 'reset_password',
            targetId: id.toString(),
            targetType: 'user',
            oldData: { username: existingUser.username },
            newData: { action: '重置密码' },
        });

        return {
            code: 0,
            message: '重置密码成功'
        }
    } catch (error) {
        console.error('重置密码失败:', error);
        return  {
            code: -1,
            message: '重置密码失败'
        }
    }
}); 