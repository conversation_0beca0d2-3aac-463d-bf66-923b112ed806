import { eq } from "drizzle-orm";
import { news } from "~/server/db/schema";
import db from "~/server/db";

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { title, content } = body;

    // 验证必填字段
    if (!title || !content) {
      return {
        code: 1,
        message: "请填写完整的新闻信息",
        data: {},
      };
    }

    // 验证标题长度
    if (title.length < 2 || title.length > 100) {
      return {
        code: 1,
        message: "新闻标题长度应在2-100个字符之间",
        data: {},
      };
    }

    // 验证内容长度
    if (content.length < 10) {
      return {
        code: 1,
        message: "新闻内容至少需要10个字符",
        data: {},
      };
    }

    // 检查是否已存在同标题新闻
    const existingNews = await db
      .select()
      .from(news)
      .where(eq(news.title, title))
      .limit(1);

    if (existingNews.length > 0) {
      return {
        code: 1,
        message: "新闻标题已存在",
        data: {},
      };
    }

    const now = new Date().toLocaleString("zh-CN", { timeZone: "Asia/Shanghai" });

    // 创建新闻数据
    await db.insert(news).values({
      title,
      content,
      createdAt: now,
      updatedAt: now,
    });

    return {
      code: 0,
      message: "创建新闻成功",
      data: {},
    };
  } catch (error) {
    console.error("创建新闻失败:", error);
    return {
      code: 1,
      message: "创建新闻失败",
      data: {},
    };
  }
}); 