import { like, and, desc, sql } from "drizzle-orm";
import { news } from "~/server/db/schema";
import db from "~/server/db";

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { page = 1, pageSize = 10, title = "" } = body;

    // 构建查询条件
    const conditions = [];
    if (title) {
      conditions.push(like(news.title, `%${title}%`));
    }

    // 查询新闻总数
    const totalQuery = db.select({ count: sql`COUNT(*)` }).from(news);
    if (conditions.length > 0) {
      totalQuery.where(and(...conditions));
    }
    const totalResult = await totalQuery;
    const total = Number(totalResult[0]?.count || 0);

    // 查询新闻列表
    const baseQuery = db.select().from(news);
    const list = await (conditions.length > 0 
      ? baseQuery.where(and(...conditions))
      : baseQuery)
      .orderBy(desc(news.createdAt))
      .limit(pageSize)
      .offset((page - 1) * pageSize);

    return {
      code: 0,
      message: "获取新闻列表成功",
      data: {
        list,
        total,
        page,
        pageSize,
      },
    };
  } catch (error) {
    console.error("获取新闻列表失败:", error);
    return {
      code: 1,
      message: "获取新闻列表失败",
      data: {},
    };
  }
}); 