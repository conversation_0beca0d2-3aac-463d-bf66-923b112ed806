import { eq, and, not } from "drizzle-orm";
import { news } from "~/server/db/schema";
import db from "~/server/db";

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id, title, content } = body;

    // 验证必填字段
    if (!id || !title || !content) {
      return {
        code: 1,
        message: "请填写完整的新闻信息",
        data: {},
      };
    }

    // 验证标题长度
    if (title.length < 2 || title.length > 100) {
      return {
        code: 1,
        message: "新闻标题长度应在2-100个字符之间",
        data: {},
      };
    }

    // 验证内容长度
    if (content.length < 10) {
      return {
        code: 1,
        message: "新闻内容至少需要10个字符",
        data: {},
      };
    }

    // 验证新闻是否存在
    const existingNews = await db.select().from(news).where(eq(news.id, id)).limit(1);
    if (!existingNews.length) {
      return {
        code: 1,
        message: "新闻不存在",
        data: {},
      };
    }

    // 检查是否已存在同标题新闻（排除当前新闻）
    const duplicateNews = await db
      .select()
      .from(news)
      .where(and(eq(news.title, title), not(eq(news.id, id))))
      .limit(1);

    if (duplicateNews.length > 0) {
      return {
        code: 1,
        message: "新闻标题已存在",
        data: {},
      };
    }

    const now = new Date().toLocaleString("zh-CN", { timeZone: "Asia/Shanghai" });

    // 更新新闻数据
    await db
      .update(news)
      .set({
        title,
        content,
        updatedAt: now,
      })
      .where(eq(news.id, id));

    return {
      code: 0,
      message: "更新新闻成功",
      data: {},
    };
  } catch (error) {
    console.error("更新新闻失败:", error);
    return {
      code: 1,
      message: "更新新闻失败",
      data: {},
    };
  }
}); 