import { eq } from "drizzle-orm";
import { news } from "~/server/db/schema";
import db from "~/server/db";

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id } = body;

    // 验证必填字段
    if (!id) {
      return {
        code: 1,
        message: "请提供新闻ID",
        data: {},
      };
    }

    // 验证新闻是否存在
    const existingNews = await db.select().from(news).where(eq(news.id, id)).limit(1);
    if (!existingNews.length) {
      return {
        code: 1,
        message: "新闻不存在",
        data: {},
      };
    }

    // 删除新闻
    await db.delete(news).where(eq(news.id, id));

    return {
      code: 0,
      message: "删除新闻成功",
      data: {},
    };
  } catch (error) {
    console.error("删除新闻失败:", error);
    return {
      code: 1,
      message: "删除新闻失败",
      data: {},
    };
  }
}); 