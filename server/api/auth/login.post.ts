import db from "~/server/db";
import { z } from "zod";
import { eq } from "drizzle-orm";
import { users, rolePermissions } from "~/server/db/schema";
import { verifyPassword } from "~/server/utils/password.util";
import { generateToken } from "~/server/utils/jwt.util";

const loginSchema = z.object({
  phone: z.string(),
  password: z.string(),
});

export default defineEventHandler(async (event) => {
  const body = await readValidatedBody(event, (body) =>
    loginSchema.safeParse(body)
  );
  if (!body.success) {
    return {
      code: -1,
      message: "请输入正确的手机号和密码",
    };
  }
  const { phone, password } = body.data;
  const user = await db.query.users.findFirst({
    where: eq(users.phone, phone),
    with: {
      role: true,
    },
  });
  if (!user) {
    return {
      code: -1,
      message: "请输入正确的手机号和密码",
    };
  }
  const isValidatePassword = await verifyPassword(password, user.password);
  if (!isValidatePassword) {
    return {
      code: -1,
      message: "请输入正确的手机号和密码",
    };
  }

  // 获取用户角色对应的权限
  const rolePerm = await db.query.rolePermissions.findMany({
    where: eq(rolePermissions.roleId, user.roleId),
    with: {
      permission: true,
    },
  });

  // 提取权限代码列表
  const permissionCodes = rolePerm.map(rp => rp.permission?.code || '');

  // 将权限信息也存入JWT
  const tokenPayload = {
    userId: user.id,
    name: user.username,
    phone: user.phone,
    roleId: user.roleId,
    roleName: user.role?.name || '',
    permissions: permissionCodes, // 添加权限信息到JWT
  };
  console.log("🔒 tokenPayload", tokenPayload);
  const token = generateToken(tokenPayload);

  return {
    code: 0,
    data: {
      user: {
        id: user.id,
        name: user.username,
        phone: user.phone,
        role: {
          id: user.roleId,
          name: user.role?.name || '',
        },
        permissions: permissionCodes,
      },
      token,
    },
    message: "登录成功",
  };
});
