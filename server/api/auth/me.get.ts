// server/api/auth/me.get.ts
import { verifyToken } from "~/server/utils/jwt.util";

export default defineEventHandler(async (event) => {
  const authHeader = getHeader(event, "Authorization");

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    throw createError({
      statusCode: 401,
      message: "Unauthorized: Missing or malformed token",
    });
  }

  const token = authHeader.substring(7); // "Bearer ".length
  const decodedPayload = verifyToken(token);

  if (!decodedPayload) {
    // console.log('ME API: Invalid token');
    // return null; // 或者抛出401错误
    throw createError({
      statusCode: 401,
      message: "Unauthorized: Invalid token",
    });
  }

  // decodedPayload 包含 { userId, email }
  try {
    // 判断是否需要刷新用户信息
    // 通常情况下，JWT中包含的信息足够使用
    // 如果需要确保最新数据，可以选择性地从数据库获取
    
    // 直接从JWT中获取信息
    const userData = {
      id: decodedPayload.userId,
      name: decodedPayload.name,
      role: {
        id: decodedPayload.roleId,
        name: decodedPayload.roleName,
      },
      permissions: decodedPayload.permissions || [],
    };

    // 如果需要每次都获取最新数据，比如用户信息可能被其他地方修改过
    // 可以根据业务需求放开下面的注释代码
    /*
    // 获取最新的用户信息
    const user = await db.query.users.findFirst({
      where: eq(users.id, decodedPayload.userId),
      with: {
        role: true,
      },
    });

    if (!user) {
      throw createError({
        statusCode: 404,
        message: "User not found",
      });
    }
    
    // 使用数据库中的最新用户信息
    userData.name = user.username;
    userData.role.name = user.role?.name || userData.role.name;
    */

    return {
      code: 0,
      data: userData,
      message: "success",
    };
  } catch (error) {
    console.error("Error fetching current user (me.get.ts):", error);
    throw createError({ 
      statusCode: 500, 
      message: 'Error fetching user data'
    });
  }
});
