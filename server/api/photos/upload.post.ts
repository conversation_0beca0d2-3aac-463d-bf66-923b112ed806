import COS from 'cos-nodejs-sdk-v5'
import { nanoid } from 'nanoid'

export default defineEventHandler(async (event) => {
  try {
    const formData = await readMultipartFormData(event)
    
    if (!formData || formData.length === 0) {
      return {
        code: 400,
        message: '没有上传文件',
        data: {}
      }
    }

    // 验证环境变量
    if (!process.env.COS_SECRET_ID || !process.env.COS_SECRET_KEY || !process.env.COS_BUCKET || !process.env.COS_REGION) {
      console.error('腾讯云COS配置缺失:', {
        COS_SECRET_ID: !!process.env.COS_SECRET_ID,
        COS_SECRET_KEY: !!process.env.COS_SECRET_KEY,
        COS_BUCKET: !!process.env.COS_BUCKET,
        COS_REGION: !!process.env.COS_REGION
      })
      return {
        code: 500,
        message: '腾讯云COS配置缺失，请检查环境变量',
        data: {}
      }
    }

    // 配置腾讯云COS客户端
    const cos = new COS({
      SecretId: process.env.COS_SECRET_ID,
      SecretKey: process.env.COS_SECRET_KEY,
    })

    const bucket = process.env.COS_BUCKET
    const region = process.env.COS_REGION

    console.log('腾讯云COS客户端配置:', {
      bucket,
      region,
      hasSecretId: !!process.env.COS_SECRET_ID,
      hasSecretKey: !!process.env.COS_SECRET_KEY
    })

    const uploadedFiles = []

    for (const file of formData) {
      if (file.name === 'files' && file.filename && file.data) {
        // 检查文件大小（1MB限制）
        if (file.data.length > 1024 * 1024) {
          return {
            code: 400,
            message: `文件 ${file.filename} 超过1MB限制`,
            data: {}
          }
        }

        // 检查文件类型
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
        if (!allowedTypes.includes(file.type || '')) {
          return {
            code: 400,
            message: `文件 ${file.filename} 不是支持的图片格式`,
            data: {}
          }
        }

        // 生成唯一文件名
        const fileExtension = file.filename.split('.').pop()
        const uniqueFileName = `${nanoid(10)}-${Date.now()}.${fileExtension}`
        const objectKey = `photos/${uniqueFileName}`

        // 上传到腾讯云COS
        const uploadResult = await new Promise((resolve, reject) => {
          cos.putObject({
            Bucket: bucket,
            Region: region,
            Key: objectKey,
            Body: file.data,
            ContentLength: file.data.length,
            ContentType: file.type,
          }, (err, data) => {
            if (err) {
              reject(err)
            } else {
              resolve(data)
            }
          })
        })

        // 生成访问URL
        const fileUrl = `https://${bucket}.cos.${region}.myqcloud.com/${objectKey}`

        uploadedFiles.push({
          originalName: file.filename,
          fileName: uniqueFileName,
          url: fileUrl,
          size: file.data.length
        })
      }
    }

    if (uploadedFiles.length === 0) {
      return {
        code: 400,
        message: '没有有效的图片文件',
        data: {}
      }
    }

    return {
      code: 0,
      message: '上传成功',
      data: {
        files: uploadedFiles
      }
    }
  } catch (error: any) {
    console.error('上传文件失败:', error)
    return {
      code: error.statusCode || 500,
      message: error.message || '上传文件失败',
      data: {}
    }
  }
}) 