import { photos, lands, crops, contractLands, tenants } from '~/server/db/schema';
import db from '~/server/db';
import { eq } from 'drizzle-orm';
import dayjs from 'dayjs';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { name, url, landId, cropId } = body;

    // 验证必填字段
    if (!name || !url || !landId) {
      return {
        code: 400,
        message: '照片名称、URL和地块不能为空',
        data: {}
      };
    }

    // 验证地块是否存在
    const land = await db.select().from(lands).where(eq(lands.id, landId));
    if (land.length === 0) {
      return {
        code: 400,
        message: '选择的地块不存在',
        data: {}
      };
    }

    // 获取当前租赁该地块的租户信息
    const contractLand = await db
      .select({
        tenantId: contractLands.tenantId,
        contractId: contractLands.contractId
      })
      .from(contractLands)
      .where(eq(contractLands.landId, landId))
      .limit(1);

    if (contractLand.length === 0) {
      return {
        code: 400,
        message: '该地块当前没有租户',
        data: {}
      };
    }

    const tenantId = contractLand[0].tenantId;

    // 如果指定了作物ID，验证作物是否存在且属于该地块
    if (cropId) {
      const crop = await db.select().from(crops).where(eq(crops.id, cropId));
      if (crop.length === 0) {
        return {
          code: 400,
          message: '选择的作物不存在',
          data: {}
        };
      }
      if (crop[0].landId !== landId) {
        return {
          code: 400,
          message: '选择的作物不属于该地块',
          data: {}
        };
      }
    }

    // 保存照片
    await db.insert(photos).values({
      name,
      url,
      landId,
      cropId: cropId || '',
      tenantId,
      createdAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
    });

    return {
      code: 0,
      message: '照片保存成功',
      data: {}
    };
  } catch (error: any) {
    console.error('保存照片失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '保存照片失败',
      data: {}
    };
  }
}); 