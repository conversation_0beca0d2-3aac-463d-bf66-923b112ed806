import { photos, lands, crops, tenants } from '~/server/db/schema';
import db from '~/server/db';
import { eq, desc, sql } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { page = 1, pageSize = 20, landId } = body;

    const offset = (page - 1) * pageSize;

    // 构建查询条件
    let whereCondition = undefined;
    if (landId) {
      whereCondition = eq(photos.landId, landId);
    }

    // 查询照片列表，关联地块、作物、租户信息
    const photoList = await db
      .select({
        id: photos.id,
        name: photos.name,
        url: photos.url,
        landId: photos.landId,
        cropId: photos.cropId,
        tenantId: photos.tenantId,
        createdAt: photos.createdAt,
        updatedAt: photos.updatedAt,
        landName: lands.name,
        cropName: crops.name,
        cropEmoji: crops.emoji,
        tenantName: tenants.name,
      })
      .from(photos)
      .leftJoin(lands, eq(photos.landId, lands.id))
      .leftJoin(crops, eq(photos.cropId, crops.id))
      .leftJoin(tenants, eq(photos.tenantId, tenants.id))
      .where(whereCondition)
      .orderBy(desc(photos.createdAt))
      .limit(pageSize)
      .offset(offset);

    // 查询总数
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(photos)
      .where(whereCondition);
    
    const total = totalResult[0]?.count || 0;

    // 按地块分组
    const groupedPhotos = photoList.reduce((acc, photo) => {
      const landKey = photo.landId;
      if (!acc[landKey]) {
        acc[landKey] = {
          landId: photo.landId,
          landName: photo.landName,
          photos: []
        };
      }
      acc[landKey].photos.push(photo);
      return acc;
    }, {} as Record<string, any>);

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: Object.values(groupedPhotos),
        total,
        page,
        pageSize
      }
    };
  } catch (error: any) {
    console.error('获取照片列表失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '获取照片列表失败',
      data: {}
    };
  }
}); 