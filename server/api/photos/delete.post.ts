import { photos } from '~/server/db/schema';
import db from '~/server/db';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id } = body;

    if (!id) {
      return {
        code: 400,
        message: '照片ID不能为空',
        data: {}
      };
    }

    // 检查照片是否存在
    const existingPhoto = await db.select().from(photos).where(eq(photos.id, id));
    if (existingPhoto.length === 0) {
      return {
        code: 404,
        message: '照片不存在',
        data: {}
      };
    }

    // 删除照片记录
    await db.delete(photos).where(eq(photos.id, id));

    return {
      code: 0,
      message: '删除成功',
      data: {}
    };
  } catch (error: any) {
    console.error('删除照片失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '删除照片失败',
      data: {}
    };
  }
}); 