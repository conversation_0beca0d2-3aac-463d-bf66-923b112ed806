import { desc, like, and } from 'drizzle-orm';
import { tenants } from '~/server/db/schema';
import db from '~/server/db';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { pageSize = 10, current = 1, keyword } = body;

    // 构建查询条件
    const conditions = [];
    if (keyword) {
      conditions.push(like(tenants.name, `%${keyword}%`));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // 查询总数
    const totalResult = await db.select().from(tenants).where(whereClause);
    const total = totalResult.length;

    // 查询分页数据
    const offset = (current - 1) * pageSize;
    const result = await db
      .select({
        id: tenants.id,
        name: tenants.name,
        phone: tenants.phone,
        idCard: tenants.idCard,
        status: tenants.status,
        createdAt: tenants.createdAt,
        updatedAt: tenants.updatedAt
      })
      .from(tenants)
      .where(whereClause)
      .orderBy(desc(tenants.createdAt))
      .limit(pageSize)
      .offset(offset);

    return {
      code: 0,
      message: '获取租户列表成功',
      data: {
        list: result,
        total,
        pageSize,
        current
      }
    };
  } catch (error: any) {
    console.error('获取租户列表失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '获取租户列表失败',
      data: {}
    };
  }
}); 