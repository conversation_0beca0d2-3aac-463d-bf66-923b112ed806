import { tenants } from '~/server/db/schema';
import db from '~/server/db';
import { eq, and, ne, or } from 'drizzle-orm';
import dayjs from 'dayjs';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id, name, phone, idCard, status } = body;

    // 验证必填字段
    if (!id || !name || !phone || !idCard) {
      return {
        code: 400,
        message: '租户ID、姓名、电话和身份证号不能为空',
        data: {}
      };
    }

    // 验证身份证号格式
    const idCardRegex = /^[1-9]\d{5}(18|19|20|21|22)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idCardRegex.test(idCard)) {
      return {
        code: 400,
        message: '请输入正确的身份证号',
        data: {}
      };
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return {
        code: 400,
        message: '请输入正确的手机号',
        data: {}
      };
    }

    // 检查租户是否存在
    const existingTenant = await db.select().from(tenants).where(eq(tenants.id, id));
    if (existingTenant.length === 0) {
      return {
        code: 404,
        message: '租户不存在',
        data: {}
      };
    }

    // 检查手机号或身份证号是否已被其他租户使用
    const duplicateCheck = await db.select().from(tenants).where(
      and(
        or(eq(tenants.phone, phone), eq(tenants.idCard, idCard)),
        ne(tenants.id, id)
      )
    );
    if (duplicateCheck.length > 0) {
      return {
        code: 400,
        message: '手机号或身份证号已被其他租户使用',
        data: {}
      };
    }

    // 更新租户
    await db.update(tenants).set({
      name,
      phone,
      idCard,
      status,
      updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
    }).where(eq(tenants.id, id));

    return {
      code: 0,
      message: '租户更新成功',
      data: {}
    };
  } catch (error: any) {
    console.error('更新租户失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '更新租户失败',
      data: {}
    };
  }
}); 