import { tenants } from '~/server/db/schema';
import db from '~/server/db';
import { eq, or } from 'drizzle-orm';
import dayjs from 'dayjs';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { name, phone, idCard, status = 1 } = body;

    // 验证必填字段
    if (!name || !phone || !idCard) {
      return {
        code: 400,
        message: '租户姓名、电话和身份证号不能为空',
        data: {}
      };
    }

    // 验证身份证号格式
    const idCardRegex = /^[1-9]\d{5}(18|19|20|21|22)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idCardRegex.test(idCard)) {
      return {
        code: 400,
        message: '请输入正确的身份证号',
        data: {}
      };
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return {
        code: 400,
        message: '请输入正确的手机号',
        data: {}
      };
    }

    // 检查手机号或身份证号是否已存在
    const existingTenant = await db.select().from(tenants).where(
      or(eq(tenants.phone, phone), eq(tenants.idCard, idCard))
    );
    if (existingTenant.length > 0) {
      return {
        code: 400,
        message: '手机号或身份证号已存在',
        data: {}
      };
    }

    // 创建租户
    await db.insert(tenants).values({
      name,
      phone,
      idCard,
      status,
      createdAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
    });

    return {
      code: 0,
      message: '租户创建成功',
      data: {}
    };
  } catch (error: any) {
    console.error('创建租户失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '创建租户失败',
      data: {}
    };
  }
}); 