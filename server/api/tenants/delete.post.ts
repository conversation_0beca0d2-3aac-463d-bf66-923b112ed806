import { tenants } from '~/server/db/schema';
import db from '~/server/db';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id } = body;

    // 验证必填字段
    if (!id) {
      return {
        code: 400,
        message: '租户ID不能为空',
        data: {}
      };
    }

    // 检查租户是否存在
    const existingTenant = await db.select().from(tenants).where(eq(tenants.id, id));
    if (existingTenant.length === 0) {
      return {
        code: 404,
        message: '租户不存在',
        data: {}
      };
    }

    // 删除租户
    await db.delete(tenants).where(eq(tenants.id, id));

    return {
      code: 0,
      message: '租户删除成功',
      data: {}
    };
  } catch (error: any) {
    console.error('删除租户失败:', error);
    return {
      code: error.statusCode || 500,
      message: error.statusMessage || '删除租户失败',
      data: {}
    };
  }
}); 