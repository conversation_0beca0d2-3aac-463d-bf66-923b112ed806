import { eq } from "drizzle-orm";
import { crops } from "~/server/db/schema";
import db from "~/server/db";

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id } = body;

    if (!id) {
      return {
        code: 1,
        message: "作物ID不能为空",
        data: {},
      };
    }

    // 验证作物是否存在
    const existingCrop = await db.select().from(crops).where(eq(crops.id, id)).limit(1);
    if (!existingCrop.length) {
      return {
        code: 1,
        message: "作物不存在",
        data: {},
      };
    }

    // 删除作物
    await db.delete(crops).where(eq(crops.id, id));

    return {
      code: 0,
      message: "删除作物成功",
      data: {},
    };
  } catch (error) {
    console.error("删除作物失败:", error);
    return {
      code: 1,
      message: "删除作物失败",
      data: {},
    };
  }
}); 