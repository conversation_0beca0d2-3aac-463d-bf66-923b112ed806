import { and, eq } from "drizzle-orm";
import { crops, lands } from "~/server/db/schema";
import db from "~/server/db";

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { emoji, name, landId, plantingArea, plantingDate, expectedHarvestDate, actualHarvestDate, status } = body;

    // 验证必填字段
    if (!name || !landId || !plantingArea || !plantingDate || !expectedHarvestDate) {
      return {
        code: 1,
        message: "请填写完整的作物信息",
        data: {},
      };
    }

    // 验证地块是否存在
    const land = await db.select().from(lands).where(eq(lands.id, landId)).limit(1);
    if (!land.length) {
      return {
        code: 1,
        message: "选择的地块不存在",
        data: {},
      };
    }

    // 验证种植面积不能超过地块面积
    if (parseFloat(plantingArea) > parseFloat(land[0].area)) {
      return {
        code: 1,
        message: "种植面积不能超过地块面积",
        data: {},
      };
    }

    // 检查是否已存在同名作物
    const existingCrop = await db
      .select()
      .from(crops)
      .where(and(eq(crops.name, name), eq(crops.landId, landId)))
      .limit(1);

    if (existingCrop.length > 0) {
      return {
        code: 1,
        message: "作物名称已存在",
        data: {},
      };
    }

    const now = new Date().toLocaleString("zh-CN", { timeZone: "Asia/Shanghai" });

    // 插入作物数据
    await db.insert(crops).values({
      emoji: emoji || '🌱',
      name,
      landId,
      plantingArea,
      plantingDate: new Date(plantingDate),
      expectedHarvestDate: new Date(expectedHarvestDate),
      actualHarvestDate: actualHarvestDate ? new Date(actualHarvestDate) : null,
      createdAt: now,
      updatedAt: now,
      status,
    });

    return {
      code: 0,
      message: "新增作物成功",
      data: {},
    };
  } catch (error) {
    console.error("新增作物失败:", error);
    return {
      code: 1,
      message: "新增作物失败",
      data: {},
    };
  }
}); 