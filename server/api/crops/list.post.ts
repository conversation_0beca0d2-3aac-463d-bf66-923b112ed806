import { eq, like, and, desc, sql } from "drizzle-orm";
import { crops, lands } from "~/server/db/schema";
import db from "~/server/db";

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { page = 1, pageSize = 10, name, landId } = body;

    // 构建查询条件
    const conditions = [];
    if (name) {
      conditions.push(like(crops.name, `%${name}%`));
    }
    if (landId) {
      conditions.push(eq(crops.landId, landId));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // 查询总数
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(crops)
      .where(whereClause);
    const total = totalResult[0].count;

    // 查询数据，联表获取地块名称
    const list = await db
      .select({
        id: crops.id,
        emoji: crops.emoji,
        name: crops.name,
        landId: crops.landId,
        landName: lands.name,
        status: crops.status,
        plantingArea: crops.plantingArea,
        plantingDate: crops.plantingDate,
        expectedHarvestDate: crops.expectedHarvestDate,
        actualHarvestDate: crops.actualHarvestDate,
        createdAt: crops.createdAt,
        updatedAt: crops.updatedAt,
      })
      .from(crops)
      .leftJoin(lands, eq(crops.landId, lands.id))
      .where(whereClause)
      .orderBy(desc(crops.createdAt))
      .limit(pageSize)
      .offset((page - 1) * pageSize);

    return {
      code: 0,
      message: "获取作物列表成功",
      data: {
        list,
        total,
        page,
        pageSize,
      },
    };
  } catch (error) {
    console.error("获取作物列表失败:", error);
    return {
      code: 1,
      message: "获取作物列表失败",
      data: {},
    };
  }
}); 