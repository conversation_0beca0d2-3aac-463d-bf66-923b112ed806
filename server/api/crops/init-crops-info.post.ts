import db from "~/server/db";
import { cropsInfo } from "~/server/db/schema";

export default defineEventHandler(async (event) => {
  try {
    // 检查是否已经有数据
    const existingData = await db.select().from(cropsInfo).limit(1);
    if (existingData.length > 0) {
      return {
        code: 0,
        message: "作物信息已存在，无需初始化",
        data: {},
      };
    }

    // 初始化作物信息数据
    const cropsData = [
      // 蔬菜类
      { emoji: '🥬', name: '白菜', growthDays: 60, remarks: '适合春秋种植，喜凉爽气候' },
      { emoji: '🥒', name: '黄瓜', growthDays: 55, remarks: '需要充足阳光和水分' },
      { emoji: '🍅', name: '番茄', growthDays: 75, remarks: '需要支架，定期修剪' },
      { emoji: '🥕', name: '胡萝卜', growthDays: 70, remarks: '需要深松土壤，避免分叉' },
      { emoji: '🌶️', name: '辣椒', growthDays: 80, remarks: '喜温暖，需要充足阳光' },
      { emoji: '🫑', name: '青椒', growthDays: 75, remarks: '需要温暖湿润环境' },
      { emoji: '🥔', name: '土豆', growthDays: 90, remarks: '需要疏松土壤，避免积水' },
      { emoji: '🧅', name: '洋葱', growthDays: 120, remarks: '生长期较长，需要充足阳光' },
      { emoji: '🧄', name: '大蒜', growthDays: 180, remarks: '秋播春收，需要低温春化' },
      { emoji: '🥦', name: '西兰花', growthDays: 85, remarks: '喜凉爽气候，需要充足水分' },
      { emoji: '🍆', name: '茄子', growthDays: 90, remarks: '喜温暖，需要充足阳光' },
      { emoji: '🌽', name: '玉米', growthDays: 100, remarks: '需要充足阳光和水分' },
      { emoji: '🫛', name: '豌豆', growthDays: 65, remarks: '喜凉爽气候，春秋种植' },
      { emoji: '🍄', name: '蘑菇', growthDays: 30, remarks: '需要阴凉湿润环境' },
      
      // 水果类
      { emoji: '🍓', name: '草莓', growthDays: 90, remarks: '需要充足阳光，定期施肥' },
      { emoji: '🍉', name: '西瓜', growthDays: 95, remarks: '需要大量空间和充足阳光' },
      { emoji: '🍈', name: '哈密瓜', growthDays: 100, remarks: '需要温暖干燥气候' },
      
      // 粮食作物
      { emoji: '🌾', name: '小麦', growthDays: 120, remarks: '秋播春收，需要充足阳光' },
      { emoji: '🫘', name: '大豆', growthDays: 110, remarks: '需要充足阳光和适量水分' },
      { emoji: '🥜', name: '花生', growthDays: 120, remarks: '需要疏松沙质土壤' },
      { emoji: '🌻', name: '向日葵', growthDays: 85, remarks: '需要充足阳光，耐旱性强' },
      
      // 中草药和其他
      { emoji: '🌿', name: '香草', growthDays: 45, remarks: '需要充足阳光，定期修剪' },
      { emoji: '🌱', name: '豆苗', growthDays: 15, remarks: '生长快速，适合室内种植' },
      { emoji: '☘️', name: '三叶草', growthDays: 40, remarks: '适应性强，可作绿肥' },
    ];

    // 批量插入数据
    await db.insert(cropsInfo).values(cropsData);

    return {
      code: 0,
      message: "作物信息初始化成功",
      data: { count: cropsData.length },
    };
  } catch (error) {
    console.error("初始化作物信息失败:", error);
    return {
      code: 1,
      message: "初始化作物信息失败",
      data: {},
    };
  }
});
