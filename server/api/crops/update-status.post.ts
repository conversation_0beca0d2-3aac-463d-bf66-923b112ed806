import { eq } from "drizzle-orm";
import { crops } from "~/server/db/schema";
import db from "~/server/db";

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id, status } = body;

    // 验证必填字段
    if (!id || status === undefined) {
      return {
        code: 1,
        message: "请提供作物ID和状态",
        data: {},
      };
    }

    // 验证状态值
    if (![1, 2, 3].includes(status)) {
      return {
        code: 1,
        message: "无效的状态值",
        data: {},
      };
    }

    // 验证作物是否存在
    const existingCrop = await db.select().from(crops).where(eq(crops.id, id)).limit(1);
    if (!existingCrop.length) {
      return {
        code: 1,
        message: "作物不存在",
        data: {},
      };
    }

    const now = new Date().toLocaleString("zh-CN", { timeZone: "Asia/Shanghai" });

    // 更新作物状态
    await db
      .update(crops)
      .set({
        status,
        updatedAt: now,
      })
      .where(eq(crops.id, id));

    return {
      code: 0,
      message: "更新作物状态成功",
      data: {},
    };
  } catch (error) {
    console.error("更新作物状态失败:", error);
    return {
      code: 1,
      message: "更新作物状态失败",
      data: {},
    };
  }
}); 