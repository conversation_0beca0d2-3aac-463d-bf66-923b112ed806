import db from "~/server/db";
import { cropsInfo } from "~/server/db/schema";

export default defineEventHandler(async (event) => {
  try {
    // 获取所有作物信息
    const list = await db
      .select({
        id: cropsInfo.id,
        emoji: cropsInfo.emoji,
        name: cropsInfo.name,
        growthDays: cropsInfo.growthDays,
        remarks: cropsInfo.remarks,
      })
      .from(cropsInfo)
      .orderBy(cropsInfo.name);

    return {
      code: 0,
      message: "获取作物信息成功",
      data: list,
    };
  } catch (error) {
    console.error("获取作物信息失败:", error);
    return {
      code: 1,
      message: "获取作物信息失败",
      data: [],
    };
  }
});
