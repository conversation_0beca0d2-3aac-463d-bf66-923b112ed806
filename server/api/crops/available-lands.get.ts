import { asc } from "drizzle-orm";
import { lands } from "~/server/db/schema";
import db from "~/server/db";

export default defineEventHandler(async (event) => {
  try {
    // 获取所有地块
    const landList = await db
      .select({
        id: lands.id,
        name: lands.name,
        area: lands.area,
        status: lands.status,
      })
      .from(lands)
      .orderBy(asc(lands.name));

    return {
      code: 0,
      message: "获取地块列表成功",
      data: landList,
    };
  } catch (error) {
    console.error("获取地块列表失败:", error);
    return {
      code: 1,
      message: "获取地块列表失败",
      data: [],
    };
  }
}); 