// https://nuxt.com/docs/api/configuration/nuxt-config
import tailwindcss from "@tailwindcss/vite";
export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  modules: ['@nuxt/icon', '@nuxt/image', '@tdesign-vue-next/nuxt'],
  css: ['@/assets/css/main.css', '@/assets/css/nprogress.css'],
  vite: {
    plugins: [
      tailwindcss(),
    ],
  },
  runtimeConfig: {
    // 私有配置（仅在服务端可用）
    databaseUrl: process.env.DATABASE_URL || '',
    databaseUser: process.env.DATABASE_USER || '',
    databasePassword: process.env.DATABASE_PASSWORD || '',
    databaseHost: process.env.DATABASE_HOST || 'localhost',
    databasePort: process.env.DATABASE_PORT || '3306',
    databaseName: process.env.DATABASE_NAME || '',
    jwtSecret: process.env.JWT_SECRET || '',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',
    // 公共配置（客户端也可用）
    public: {
      appName: process.env.APP_NAME || '盂县联众种植专业合作社',
      appBaseUrl: process.env.APP_BASE_URL || 'http://localhost:3000'
    }
  },
  icon: {
    mode: 'css',
    cssLayer: 'base'
  },
  devServer: {
    port: 6339
  }
})