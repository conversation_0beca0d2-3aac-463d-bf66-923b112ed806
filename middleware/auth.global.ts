// middleware/auth.global.ts (或 middleware/auth.ts)

export default defineNuxtRouteMiddleware(async (to, from) => {
    const { user, fetchUser, isLoggedIn } = useAuth();
  
    // 关键点：在客户端，如果 authToken 存在但 user 状态未知，则尝试获取
    // 这通常在页面刷新或直接访问受保护路由时发生
    const { authToken } = useAuth(); // 获取 token 状态
    if (process.client && authToken.value && !user.value) {
      // console.log('Auth Middleware: Token exists, user data missing, fetching user...');
      await fetchUser();
    }
    
    const requiresAuth = to.meta.requiresAuth ?? (to.path.startsWith('/dashboard')); // 简化：/dashboard 需要认证
  
    if (requiresAuth && !isLoggedIn.value) {
      if (process.client) {
        // console.log(`Auth Middleware: Access to ${to.path} denied, redirecting to login.`);
        return navigateTo('/?redirect=' + to.fullPath);
      }
      // 服务器端，如果未登录，并且访问受保护路由，通常会渲染一个未授权的状态
      // 或者，如果 fetchUser 在服务器端能通过 cookie (如果同时使用) 或其他方式确定用户，那也可以。
      // 但对于纯 JWT，服务器端首次渲染时，请求头通常不由浏览器自动附加，所以 isLoggedIn 在 SSR 阶段可能为 false。
      // 这种情况下，让客户端处理重定向更简单。
    }
  
    if (isLoggedIn.value && (to.path === '/' || to.path === '/register')) {
      if (process.client) {
        return navigateTo('/dashboard');
      }
    }
  });