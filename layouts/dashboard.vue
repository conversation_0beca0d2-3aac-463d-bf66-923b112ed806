<template>
  <div class="dashboard-container" :class="{ 'sidebar-collapsed': isSidebarCollapsed }">
    <!-- 左侧菜单 -->
    <div class="sidebar">
      <div class="logo">
        <h2>盂县联众种植合作社</h2>
      </div>
      
      <!-- 使用TDesign菜单组件 -->
      <Menus class="dashboard-menu"/>
    </div>

    <!-- 右侧内容区域 -->
    <div class="content-area">
      <header class="header">
        <div class="header-left">
          <button class="toggle-btn" @click="toggleSidebar">☰</button>
          
        </div>
        <div class="header-right">
          <div class="user-info">
            <ClientOnly>
              <span v-if="user" class="user-name">{{ user.name }}</span>
              <span v-if="user?.role" class="user-role">({{ user.role.name }})</span>
              <t-dropdown :options="userOptions" @click="handleUserAction">
                <span class="avatar"><Icon name="lucide:user" /></span>
              </t-dropdown>
            </ClientOnly>
          </div>
        </div>
      </header>
      
      <main class="main-content">
        <!-- 页面内容区域 -->
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { DialogPlugin } from 'tdesign-vue-next';

const router = useRouter();
const { user, logout } = useAuth();

// 侧边栏折叠状态
const isSidebarCollapsed = ref(false);

// 检查屏幕宽度
const checkScreenSize = () => {
  const width = window.innerWidth;
  
  // 如果屏幕宽度小于768px，自动收起侧边栏
  if (width < 768) {
    isSidebarCollapsed.value = true;
  }
  // 如果屏幕宽度大于等于768px，自动展开侧边栏
  else {
    isSidebarCollapsed.value = false;
  }
};

// 切换侧边栏显示/隐藏
const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

// 用户下拉菜单选项
const userOptions = [
  {
    content: '个人设置',
    value: 'profile',
  },
  {
    content: '安全退出',
    value: 'logout',
  },
];

// 处理用户操作
const handleUserAction = (data) => {
  const { value } = data;
  if (value === 'profile') {
    router.push('/dashboard/me');
  } else if (value === 'logout') {
    const confirm = DialogPlugin.confirm({
      header: '提示',
      body: '确定要退出登录吗？',
      onConfirm: () => {
        logout();
        confirm.hide();
      },
    });
  }
};
</script>

<style scoped>
.dashboard-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
  width: 240px;
  min-width: 240px;
  background-color: #fff;
  color: #a2a3b7;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100vh;
  position: relative;
}

/* 侧边栏折叠状态 */
.dashboard-container.sidebar-collapsed .sidebar {
  width: 0;
  min-width: 0;
}

.logo {
  padding: 20px;
  border-bottom: 1px solid #ffffff;
  text-align: center;
  white-space: nowrap;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.logo h2 {
  color: #4c9359;
  margin: 0;
  font-weight: bold;
}

.dashboard-menu {
  position: absolute;
  top: 81px;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 20px;
}

/* 内容区域样式 */
.content-area {
  flex: 1;
  background-color: #f5f5f9;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.header {
  background-color: white;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
}

.toggle-btn {
  background: none;
  border: none;
  font-size: 20px;
  margin-right: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 4px;
}

.toggle-btn:hover {
  background-color: #f0f0f0;
}

.header-right .user-info {
  display: flex;
  align-items: center;
}

.user-name {
  margin-right: 5px;
}

.user-role {
  color: #6c757d;
  font-size: 0.9em;
  margin-right: 10px;
}

.avatar {
  font-size: 20px;
  cursor: pointer;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .main-content {
    padding: 15px;
  }
  
  .header {
    padding: 0 15px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .header {
    padding: 0 10px;
  }
  
  .main-content {
    padding: 10px;
  }
  
  .user-name,
  .user-role {
    display: none;
  }
}
</style> 