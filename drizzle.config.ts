import type { Config } from 'drizzle-kit';
import 'dotenv/config'; // 确保可以加载 .env 文件中的环境变量

if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL is not set in .env file');
}

export default {
  schema: './server/db/schema.ts',
  out: './server/db/migrations',
  dialect: 'mysql', // <--- 修改为 mysql2
  dbCredentials: {
    url: process.env.DATABASE_URL, // <--- 使用连接字符串
  },
  verbose: true,
  strict: true,
} satisfies Config;