// plugins/auth.ts
export default defineNuxtPlugin(async (nuxtApp) => {
    const { fetchUser } = useAuth();
    const { authToken } = useAuth(); // 获取 token 状态
  
    const handler = async () => {
      // 只有当 token 存在时才尝试获取用户信息
      if (authToken.value) {
        // console.log('Auth Plugin: Token found, fetching user...');
        await fetchUser();
      } else {
        // console.log('Auth Plugin: No token found, skipping user fetch.');
      }
    };
  
    if (import.meta.server) {
      // 在服务器端，authToken 可能尚未从客户端 localStorage 加载（因为它只在客户端）。
      // 如果你的 JWT 是通过 cookie 传递给服务器的（例如，在 SSR 期间手动设置），这里可以处理。
      // 但对于典型的 localStorage JWT 方案，服务器端插件通常不会有 token。
      // 你可以通过 hook 确保 useAuth composable 中的客户端 localStorage 加载逻辑先运行。
      // 不过，最常见的模式是服务器端初始渲染时不一定有用户，客户端水合后再 fetchUser。
    }
  
    // 对于客户端，在 app 挂载后，useAuth composable 应该已经尝试从 localStorage 加载了 token。
    nuxtApp.hook('app:mounted', handler);
  
    // 如果你希望在每次路由变更后都尝试（可能冗余，因为中间件也会做）
    // nuxtApp.hook('page:finish', handler);
  });
